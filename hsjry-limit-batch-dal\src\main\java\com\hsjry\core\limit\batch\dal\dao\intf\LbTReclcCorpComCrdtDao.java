package com.hsjry.core.limit.batch.dal.dao.intf;

import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcCorpComCrdtDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbTReclcCorpComCrdtQuery;

import java.util.List;

import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度中心-中间表-额度重算中对公客户中一般授信额度数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-08-21 11:31:28
 */
public interface LbTReclcCorpComCrdtDao extends IBaseDao<LbTReclcCorpComCrdtDo> {
    /**
     * 分页查询额度中心-中间表-额度重算中对公客户中一般授信额度
     *
     * @param lbTReclcCorpComCrdtQuery 条件
     * @return PageInfo<LbTReclcCorpComCrdtDo>
     */
    PageInfo<LbTReclcCorpComCrdtDo> selectPage(LbTReclcCorpComCrdtQuery lbTReclcCorpComCrdtQuery, PageParam pageParam);

    /**
     * 根据key查询额度中心-中间表-额度重算中对公客户中一般授信额度
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    LbTReclcCorpComCrdtDo selectByKey(String custNo, String custLimitId);

    /**
     * 根据key删除额度中心-中间表-额度重算中对公客户中一般授信额度
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    int deleteByKey(String custNo, String custLimitId);

    /**
     * 查询额度中心-中间表-额度重算中对公客户中一般授信额度信息
     *
     * @param lbTReclcCorpComCrdtQuery 条件
     * @return List<LbTReclcCorpComCrdtDo>
     */
    List<LbTReclcCorpComCrdtDo> selectByExample(LbTReclcCorpComCrdtQuery lbTReclcCorpComCrdtQuery);

    /**
     * 新增额度中心-中间表-额度重算中对公客户中一般授信额度信息
     *
     * @param lbTReclcCorpComCrdt 条件
     * @return int>
     */
    int insertBySelective(LbTReclcCorpComCrdtDo lbTReclcCorpComCrdt);

    /**
     * 修改额度中心-中间表-额度重算中对公客户中一般授信额度信息
     *
     * @param lbTReclcCorpComCrdt
     * @return
     */
    int updateBySelective(LbTReclcCorpComCrdtDo lbTReclcCorpComCrdt);

    /**
     * 修改额度中心-中间表-额度重算中对公客户中一般授信额度信息
     *
     * @param lbTReclcCorpComCrdt
     * @param lbTReclcCorpComCrdtQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbTReclcCorpComCrdtDo lbTReclcCorpComCrdt,
        LbTReclcCorpComCrdtQuery lbTReclcCorpComCrdtQuery);
}
