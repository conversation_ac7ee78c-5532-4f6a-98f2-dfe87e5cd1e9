package com.hsjry.core.limit.batch.biz.job.biz.mid;

import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.hsjry.base.common.job.biz.BaseOrdinaryBiz;
import com.hsjry.base.common.job.dto.IEnumTrade;
import com.hsjry.base.common.job.dto.JobInitDto;
import com.hsjry.core.limit.batch.biz.job.JobCoreBusiness;
import com.hsjry.core.limit.batch.biz.job.sharding.JobCoreBusinessFactory;
import com.hsjry.core.limit.batch.biz.job.sharding.biz.mid.LbTCcsAcctLmtProcessImpl;
import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;

import lombok.extern.slf4j.Slf4j;

/**
 * 信用卡额度信息处理业务实现类
 * 负责信用卡额度信息的分片和业务处理
 *
 * <AUTHOR>
 * @date 2025-07-10 12:31:05
 */
@Slf4j
@Service("lbTCcsAcctLmtProcessBizImpl")
public class LbTCcsAcctLmtProcessBizImpl implements BaseOrdinaryBiz {

    @Override
    public IEnumTrade getJobTrade() {
        return EnumJobTrade.LB_T_CCS_ACCT_LMT_PROCESS;
    }

    /**
     * 执行信用卡额度信息处理的基础任务
     * 此方法是任务的入口点,负责协调整个数据处理流程
     *
     * @param jobInitDto 任务初始化参数,包含业务日期、批次流水号等信息
     */
    @Override
    public void execBaseJob(JobInitDto jobInitDto) {
        Integer businessDate = jobInitDto.getBusinessDate();
        String batchSerialNo = jobInitDto.getBatchSerialNo();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();

        String prefixLog = String.format("营运日期:[%s],批量流水号:[%s],任务交易码:[%s-%s]", businessDate,
            batchSerialNo, jobTradeCode, jobTradeDesc);

        log.info("{}========开始执行信用卡额度信息处理任务========", prefixLog);

        try {
            // 通过工厂获取具体的信用卡额度信息处理实现
            JobCoreBusiness jobCoreBusiness = JobCoreBusinessFactory.getJobCoreBusiness(jobTradeCode);
            if (Objects.isNull(jobCoreBusiness)) {
                String errorMsg = String.format("未找到任务交易码[%s]对应的信用卡额度信息处理实现类", jobTradeCode);
                log.error("{}{}", prefixLog, errorMsg);
                throw new RuntimeException(errorMsg);
            }

            // 执行前置处理
            log.info("{}开始执行前置处理", prefixLog);
            jobCoreBusiness.preHandle(jobInitDto);
            log.info("{}前置处理完成", prefixLog);

            log.info("{}信用卡额度信息处理任务执行完成,具体分片处理将由分片任务完成", prefixLog);

        } catch (Exception e) {
            log.error("{}信用卡额度信息处理任务执行失败", prefixLog, e);
            throw new RuntimeException(String.format("信用卡额度信息处理任务执行失败: %s", e.getMessage()), e);
        } finally {
            try {
                // 执行后置处理
                JobCoreBusiness jobCoreBusiness = JobCoreBusinessFactory.getJobCoreBusiness(jobTradeCode);
                if (Objects.nonNull(jobCoreBusiness)) {
                    log.info("{}开始执行后置处理", prefixLog);
                    jobCoreBusiness.afterHandle(jobInitDto);
                    log.info("{}后置处理完成", prefixLog);
                }
            } catch (Exception e) {
                log.error("{}后置处理执行失败", prefixLog, e);
                // 后置处理失败不影响主流程
            }
        }

        log.info("{}========信用卡额度信息处理任务结束========", prefixLog);
    }
} 