package com.hsjry.core.limit.center.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.center.dal.dao.model.LcCorpLmtViewDo;
import com.hsjry.core.limit.center.dal.dao.query.LcCorpLmtViewQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 对公客户额度视图数据库操作接口
 * 
 * <AUTHOR>
 * @date 2025-07-07 12:58:26
 */
public interface LcCorpLmtViewDao extends IBaseDao<LcCorpLmtViewDo> {
    /**
     * 分页查询对公客户额度视图
     *
     * @param lcCorpLmtViewQuery 条件
     * @return PageInfo<LcCorpLmtViewDo>
     */
    PageInfo<LcCorpLmtViewDo> selectPage(LcCorpLmtViewQuery lcCorpLmtViewQuery, PageParam pageParam);

    /**
     * 根据key查询对公客户额度视图
     *
     * @param lcCorpLmtViewId
     * @return
     */
    LcCorpLmtViewDo selectByKey(String lcCorpLmtViewId);
    /**
     * 根据key删除对公客户额度视图
     *
     * @param lcCorpLmtViewId
     * @return
     */
    int deleteByKey(String lcCorpLmtViewId);

    /**
     * 查询对公客户额度视图信息
     *
     * @param lcCorpLmtViewQuery 条件
     * @return List<LcCorpLmtViewDo>
     */
    List<LcCorpLmtViewDo> selectByExample(LcCorpLmtViewQuery lcCorpLmtViewQuery);

    /**
     * 新增对公客户额度视图信息
     *
     * @param lcCorpLmtView 条件
     * @return int>
     */
    int insertBySelective(LcCorpLmtViewDo lcCorpLmtView);

    /**
     * 修改对公客户额度视图信息
     *
     * @param lcCorpLmtView
     * @return
     */
    int updateBySelective(LcCorpLmtViewDo lcCorpLmtView);
    /**
     * 修改对公客户额度视图信息
     *
     * @param lcCorpLmtView
     * @param lcCorpLmtViewQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LcCorpLmtViewDo lcCorpLmtView, LcCorpLmtViewQuery lcCorpLmtViewQuery);

    /**
     * 1.处理客户信息,使用MERGE语句实现存在则更新,不存在则插入
     */
    int mergeCustomerInfo();

    /**
     * 2.更新总额度/敞口额度/敞口可用额度
     */
    int mergeTotalCreditLimit();

    /**
     * 3.更新低风险分项额度/低风险分项可用额度
     */
    int mergeLowRiskSubItemLimit();

    /**
     * 4.更新一般授信额度期限/一般授信额度期限单位
     */
    int mergeCommonCreditTerm();

    /**
     * 5.更新纯低风险额度/纯低风险可用额度/纯低风险额度期限/纯低风险额度期限单位
     */
    int mergeWholeLowRiskLimit();

    /**
     * 6.更新非授信额度
     */
    int mergeNoCreditLimit();

    /**
     * 7.更新担保额度
     */
    int mergeGuaranteeLimit();

    /**
     * 8.更新合作方额度/合作方可用额度
     */
    int mergeCoPartnerLimit();

    /**
     * 9.更新客户风险标签
     */
    int mergeCustomerRiskTag();

    /**
     * 10.更新客户中心的相关字段
     */
    int mergeCustomerCenterInfo();

    // ==================== 一般授信额度重算 ====================

    /**
     * 1.1.清空一般授信额度中间表
     */
    int truncateCommonCreditLimit();

    /**
     * 1.2.插入一般授信额度客户编号和额度编号
     */
    int insertCommonCreditLimit();

    /**
     * 1.3.更新一般授信额度中间表金额信息
     */
    int mergeCommonCreditLimitAmount();

    /**
     * 1.4.更新额度实例金额信息
     */
    int mergeCommonCreditLimitInstance();

    // ==================== 综合授信额度重算 ====================

    /**
     * 2.1.清空综合授信额度中间表
     */
    int truncateComprehensiveCreditLimit();

    /**
     * 2.2.插入综合授信额度客户编号和额度编号
     */
    int insertComprehensiveCreditLimit();

    /**
     * 2.3.更新综合授信额度中间表金额信息
     */
    int mergeComprehensiveCreditLimitAmount();

    /**
     * 2.4.更新额度实例金额信息
     */
    int mergeComprehensiveCreditLimitInstance();

    // ==================== 单笔单批额度重算 ====================

    /**
     * 3.1.清空单笔单批额度中间表
     */
    int truncateSingleBatchLimit();

    /**
     * 3.2.插入单笔单批额度客户编号和额度编号
     */
    int insertSingleBatchLimit();

    /**
     * 3.3.更新单笔单批额度中间表金额信息
     */
    int mergeSingleBatchLimitAmount();

    /**
     * 3.4.更新额度实例金额信息
     */
    int mergeSingleBatchLimitInstance();

    // ==================== 合作方层额度重算 ====================

    /**
     * 4.1.清空合作方层额度中间表
     */
    int truncateCoPartnerLevelLimit();

    /**
     * 4.2.插入合作方层额度客户编号和额度编号
     */
    int insertCoPartnerLevelLimit();

    /**
     * 4.3.更新合作方层额度中间表金额信息
     */
    int mergeCoPartnerLevelLimitAmount();

    /**
     * 4.4.更新额度实例金额信息
     */
    int mergeCoPartnerLevelLimitInstance();

    // ==================== 合作方额度重算 ====================

    /**
     * 5.1.清空合作方额度中间表
     */
    int truncateCoPartnerLimit();

    /**
     * 5.2.插入合作方额度客户编号和额度编号
     */
    int insertCoPartnerLimit();

    /**
     * 5.3.更新合作方额度中间表金额信息
     */
    int mergeCoPartnerLimitAmount();

    /**
     * 5.4.更新额度实例金额信息
     */
    int mergeCoPartnerLimitInstance();

    // ==================== 非授信额度重算 ====================

    /**
     * 6.1.清空非授信额度中间表
     */
    int truncateNoCreditLimit();

    /**
     * 6.2.插入非授信额度客户编号和额度编号
     */
    int insertNoCreditLimit();

    /**
     * 6.3.更新非授信额度中间表金额信息
     */
    int mergeNoCreditLimitAmount();

    /**
     * 6.4.更新额度实例金额信息
     */
    int mergeNoCreditLimitInstance();

    // ==================== 客户总额度重算 ====================

    /**
     * 7.1.清空客户总额度中间表
     */
    int truncateTotalLimit();

    /**
     * 7.2.插入客户总额度客户编号和额度编号
     */
    int insertTotalLimit();

    /**
     * 7.3.更新客户总额度中间表金额信息
     */
    int mergeTotalLimitAmount();

    /**
     * 7.4.更新额度实例金额信息
     */
    int mergeTotalLimitInstance();
}
