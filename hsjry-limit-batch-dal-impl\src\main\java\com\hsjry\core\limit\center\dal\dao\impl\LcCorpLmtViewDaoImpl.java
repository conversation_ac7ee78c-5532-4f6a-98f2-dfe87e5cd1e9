package com.hsjry.core.limit.center.dal.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.hsjry.base.common.utils.AppParamUtil;
import com.hsjry.core.limit.center.dal.dao.intf.LcCorpLmtViewDao;
import com.hsjry.core.limit.center.dal.dao.mapper.LcCorpLmtViewMapper;
import com.hsjry.core.limit.center.dal.dao.model.LcCorpLmtViewDo;
import com.hsjry.core.limit.center.dal.dao.model.LcCorpLmtViewExample;
import com.hsjry.core.limit.center.dal.dao.model.LcCorpLmtViewKeyDo;
import com.hsjry.core.limit.center.dal.dao.query.LcCorpLmtViewQuery;
import com.hsjry.lang.business.date.BusinessDateUtil;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;


/**
 * 对公客户额度视图数据库操作接口实现
 *
 * <AUTHOR>
 * @date 2025-07-07 12:58:26
 */
@Repository
public class LcCorpLmtViewDaoImpl extends AbstractBaseDaoImpl<LcCorpLmtViewDo, LcCorpLmtViewMapper>
    implements LcCorpLmtViewDao {
    /**
     * 分页查询
     *
     * @param lcCorpLmtView 条件
     * @param pageParam 分页参数
     * @return
     */
    @Override
    public PageInfo<LcCorpLmtViewDo> selectPage(LcCorpLmtViewQuery lcCorpLmtView, PageParam pageParam) {
        LcCorpLmtViewExample example = buildExample(lcCorpLmtView);
        return PageHelper.<LcCorpLmtViewDo>startPage(pageParam.getPageNum(), pageParam.getPageSize()).doSelectPageInfo(
            () -> getMapper().selectByExample(example));
    }

    /**
     * 根据key查询对公客户额度视图
     *
     * @param lcCorpLmtViewId
     * @return
     */
    @Override
    public LcCorpLmtViewDo selectByKey(String lcCorpLmtViewId) {
        LcCorpLmtViewKeyDo lcCorpLmtViewKeyDo = new LcCorpLmtViewKeyDo();
        lcCorpLmtViewKeyDo.setLcCorpLmtViewId(lcCorpLmtViewId);
        lcCorpLmtViewKeyDo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectByPrimaryKey(lcCorpLmtViewKeyDo);
    }

    /**
     * 根据key删除对公客户额度视图
     *
     * @param lcCorpLmtViewId
     * @return
     */
    @Override
    public int deleteByKey(String lcCorpLmtViewId) {
        LcCorpLmtViewKeyDo lcCorpLmtViewKeyDo = new LcCorpLmtViewKeyDo();
        lcCorpLmtViewKeyDo.setLcCorpLmtViewId(lcCorpLmtViewId);
        lcCorpLmtViewKeyDo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().deleteByPrimaryKey(lcCorpLmtViewKeyDo);
    }

    /**
     * 查询对公客户额度视图信息
     *
     * @param lcCorpLmtView 条件
     * @return List<LcCorpLmtViewDo>
     */
    @Override
    public List<LcCorpLmtViewDo> selectByExample(LcCorpLmtViewQuery lcCorpLmtView) {
        return getMapper().selectByExample(buildExample(lcCorpLmtView));
    }

    /**
     * 新增对公客户额度视图信息
     *
     * @param lcCorpLmtView 条件
     * @return int>
     */
    @Override
    public int insertBySelective(LcCorpLmtViewDo lcCorpLmtView) {
        if (lcCorpLmtView == null) {
            return -1;
        }

        lcCorpLmtView.setCreateTime(BusinessDateUtil.getDate());
        lcCorpLmtView.setUpdateTime(BusinessDateUtil.getDate());
        lcCorpLmtView.setTenantId(AppParamUtil.getTenantId());
        return getMapper().insertSelective(lcCorpLmtView);
    }

    /**
     * 修改对公客户额度视图信息
     *
     * @param lcCorpLmtView
     * @return
     */
    @Override
    public int updateBySelective(LcCorpLmtViewDo lcCorpLmtView) {
        if (lcCorpLmtView == null) {
            return -1;
        }
        lcCorpLmtView.setUpdateTime(BusinessDateUtil.getDate());
        lcCorpLmtView.setTenantId(AppParamUtil.getTenantId());
        return getMapper().updateByPrimaryKeySelective(lcCorpLmtView);
    }

    @Override
    public int updateBySelectiveByExample(LcCorpLmtViewDo lcCorpLmtView, LcCorpLmtViewQuery lcCorpLmtViewQuery) {
        lcCorpLmtView.setUpdateTime(BusinessDateUtil.getDate());
        return getMapper().updateByExampleSelective(lcCorpLmtView, buildExample(lcCorpLmtViewQuery));
    }

    @Override
    public int mergeCustomerInfo() {
        return getMapper().mergeCustomerInfo();
    }

    @Override
    public int mergeTotalCreditLimit() {
        return getMapper().mergeTotalCreditLimit();
    }

    @Override
    public int mergeLowRiskSubItemLimit() {
        return getMapper().mergeLowRiskSubItemLimit();
    }

    @Override
    public int mergeCommonCreditTerm() {
        return getMapper().mergeCommonCreditTerm();
    }

    @Override
    public int mergeWholeLowRiskLimit() {
        return getMapper().mergeWholeLowRiskLimit();
    }

    @Override
    public int mergeNoCreditLimit() {
        return getMapper().mergeNoCreditLimit();
    }

    @Override
    public int mergeGuaranteeLimit() {
        return getMapper().mergeGuaranteeLimit();
    }

    @Override
    public int mergeCoPartnerLimit() {
        return getMapper().mergeCoPartnerLimit();
    }

    @Override
    public int mergeCustomerRiskTag() {
        return getMapper().mergeCustomerRiskTag();
    }

    @Override
    public int mergeCustomerCenterInfo() {
        return getMapper().mergeCustomerCenterInfo();
    }

    @Override
    public int mergeTotalLimitAmount() {
        return getMapper().mergeTotalLimitAmount();
    }

    @Override
    public int mergeTotalLimitInstance() {
        return getMapper().mergeTotalLimitInstance();
    }

    @Override
    public int truncateCommonCreditLimit() {
        return getMapper().truncateCommonCreditLimit();
    }

    @Override
    public int insertCommonCreditLimit() {
        return getMapper().insertCommonCreditLimit();
    }

    @Override
    public int mergeCommonCreditLimitAmount() {
        return getMapper().mergeCommonCreditLimitAmount();
    }

    @Override
    public int mergeCommonCreditLimitInstance() {
        return getMapper().mergeCommonCreditLimitInstance();
    }

    @Override
    public int truncateComprehensiveCreditLimit() {
        return getMapper().truncateComprehensiveCreditLimit();
    }

    @Override
    public int insertComprehensiveCreditLimit() {
        return getMapper().insertComprehensiveCreditLimit();
    }

    @Override
    public int mergeComprehensiveCreditLimitAmount() {
        return getMapper().mergeComprehensiveCreditLimitAmount();
    }

    @Override
    public int mergeComprehensiveCreditLimitInstance() {
        return getMapper().mergeComprehensiveCreditLimitInstance();
    }

    @Override
    public int truncateSingleBatchLimit() {
        return getMapper().truncateSingleBatchLimit();
    }

    @Override
    public int insertSingleBatchLimit() {
        return getMapper().insertSingleBatchLimit();
    }

    @Override
    public int mergeSingleBatchLimitAmount() {
        return getMapper().mergeSingleBatchLimitAmount();
    }

    @Override
    public int mergeSingleBatchLimitInstance() {
        return getMapper().mergeSingleBatchLimitInstance();
    }

    @Override
    public int truncateCoPartnerLevelLimit() {
        return getMapper().truncateCoPartnerLevelLimit();
    }

    @Override
    public int insertCoPartnerLevelLimit() {
        return getMapper().insertCoPartnerLevelLimit();
    }

    @Override
    public int mergeCoPartnerLevelLimitAmount() {
        return getMapper().mergeCoPartnerLevelLimitAmount();
    }

    @Override
    public int mergeCoPartnerLevelLimitInstance() {
        return getMapper().mergeCoPartnerLevelLimitInstance();
    }

    @Override
    public int truncateCoPartnerLimit() {
        return getMapper().truncateCoPartnerLimit();
    }

    @Override
    public int insertCoPartnerLimit() {
        return getMapper().insertCoPartnerLimit();
    }

    @Override
    public int mergeCoPartnerLimitAmount() {
        return getMapper().mergeCoPartnerLimitAmount();
    }

    @Override
    public int mergeCoPartnerLimitInstance() {
        return getMapper().mergeCoPartnerLimitInstance();
    }

    @Override
    public int truncateNoCreditLimit() {
        return getMapper().truncateNoCreditLimit();
    }

    @Override
    public int insertNoCreditLimit() {
        return getMapper().insertNoCreditLimit();
    }

    @Override
    public int mergeNoCreditLimitAmount() {
        return getMapper().mergeNoCreditLimitAmount();
    }

    @Override
    public int mergeNoCreditLimitInstance() {
        return getMapper().mergeNoCreditLimitInstance();
    }

    @Override
    public int truncateTotalLimit() {
        return getMapper().truncateTotalLimit();
    }

    @Override
    public int insertTotalLimit() {
        return getMapper().insertTotalLimit();
    }

    /**
     * 构建对公客户额度视图Example信息
     *
     * @param lcCorpLmtView
     * @return
     */
    public LcCorpLmtViewExample buildExample(LcCorpLmtViewQuery lcCorpLmtView) {
        LcCorpLmtViewExample example = new LcCorpLmtViewExample();
        LcCorpLmtViewExample.Criteria criteria = example.createCriteria();
        criteria.andTenantIdEqualTo(AppParamUtil.getTenantId());
        if (lcCorpLmtView != null) {
            //添加查询条件
            if (null != lcCorpLmtView.getComCrdtTerm()) {
                criteria.andComCrdtTermEqualTo(lcCorpLmtView.getComCrdtTerm());
            }
            if (StringUtil.isNotEmpty(lcCorpLmtView.getOwnOrganId())) {
                criteria.andOwnOrganIdEqualTo(lcCorpLmtView.getOwnOrganId());
            }
            if (StringUtil.isNotEmpty(lcCorpLmtView.getOperatorId())) {
                criteria.andOperatorIdEqualTo(lcCorpLmtView.getOperatorId());
            }
            if (null != lcCorpLmtView.getCoPrtnAvlLmt()) {
                criteria.andCoPrtnAvlLmtEqualTo(lcCorpLmtView.getCoPrtnAvlLmt());
            }
            if (null != lcCorpLmtView.getCoPrtnLmt()) {
                criteria.andCoPrtnLmtEqualTo(lcCorpLmtView.getCoPrtnLmt());
            }
            if (null != lcCorpLmtView.getGuarLmt()) {
                criteria.andGuarLmtEqualTo(lcCorpLmtView.getGuarLmt());
            }
            if (null != lcCorpLmtView.getNoCrdtLmt()) {
                criteria.andNoCrdtLmtEqualTo(lcCorpLmtView.getNoCrdtLmt());
            }
            if (StringUtil.isNotEmpty(lcCorpLmtView.getWhlLowRiskCrdtTermUnit())) {
                criteria.andWhlLowRiskCrdtTermUnitEqualTo(lcCorpLmtView.getWhlLowRiskCrdtTermUnit());
            }
            if (null != lcCorpLmtView.getWhlLowRiskCrdtTerm()) {
                criteria.andWhlLowRiskCrdtTermEqualTo(lcCorpLmtView.getWhlLowRiskCrdtTerm());
            }
            if (null != lcCorpLmtView.getWhlLowRiskAvlLmt()) {
                criteria.andWhlLowRiskAvlLmtEqualTo(lcCorpLmtView.getWhlLowRiskAvlLmt());
            }
            if (null != lcCorpLmtView.getWhlLowRiskLmt()) {
                criteria.andWhlLowRiskLmtEqualTo(lcCorpLmtView.getWhlLowRiskLmt());
            }
            if (StringUtil.isNotEmpty(lcCorpLmtView.getComCrdtTermUnit())) {
                criteria.andComCrdtTermUnitEqualTo(lcCorpLmtView.getComCrdtTermUnit());
            }
            if (StringUtil.isNotEmpty(lcCorpLmtView.getLcCorpLmtViewId())) {
                criteria.andLcCorpLmtViewIdEqualTo(lcCorpLmtView.getLcCorpLmtViewId());
            }
            if (null != lcCorpLmtView.getLowRiskSubItmAvlLmt()) {
                criteria.andLowRiskSubItmAvlLmtEqualTo(lcCorpLmtView.getLowRiskSubItmAvlLmt());
            }
            if (null != lcCorpLmtView.getLowRiskSubItmLmt()) {
                criteria.andLowRiskSubItmLmtEqualTo(lcCorpLmtView.getLowRiskSubItmLmt());
            }
            if (null != lcCorpLmtView.getEsrAvlLmt()) {
                criteria.andEsrAvlLmtEqualTo(lcCorpLmtView.getEsrAvlLmt());
            }
            if (null != lcCorpLmtView.getEsrLmt()) {
                criteria.andEsrLmtEqualTo(lcCorpLmtView.getEsrLmt());
            }
            if (null != lcCorpLmtView.getTotlCrdtLmt()) {
                criteria.andTotlCrdtLmtEqualTo(lcCorpLmtView.getTotlCrdtLmt());
            }
            if (StringUtil.isNotEmpty(lcCorpLmtView.getBelgGrpNo())) {
                criteria.andBelgGrpNoEqualTo(lcCorpLmtView.getBelgGrpNo());
            }
            if (StringUtil.isNotEmpty(lcCorpLmtView.getBelgGrpNm())) {
                criteria.andBelgGrpNmEqualTo(lcCorpLmtView.getBelgGrpNm());
            }
            if (StringUtil.isNotEmpty(lcCorpLmtView.getBelgIndsTyp())) {
                criteria.andBelgIndsTypEqualTo(lcCorpLmtView.getBelgIndsTyp());
            }
            if (StringUtil.isNotEmpty(lcCorpLmtView.getCertNo())) {
                criteria.andCertNoEqualTo(lcCorpLmtView.getCertNo());
            }
            if (StringUtil.isNotEmpty(lcCorpLmtView.getCertTyp())) {
                criteria.andCertTypEqualTo(lcCorpLmtView.getCertTyp());
            }
            if (StringUtil.isNotEmpty(lcCorpLmtView.getCustNo())) {
                criteria.andCustNoEqualTo(lcCorpLmtView.getCustNo());
            }
            if (StringUtil.isNotEmpty(lcCorpLmtView.getCustNm())) {
                criteria.andCustNmEqualTo(lcCorpLmtView.getCustNm());
            }
            if (StringUtil.isNotEmpty(lcCorpLmtView.getCustRiskTag())) {
                criteria.andCustRiskTagEqualTo(lcCorpLmtView.getCustRiskTag());
            }
        }
        buildExampleExt(lcCorpLmtView, criteria);
        return example;
    }

    /**
     * 构建对公客户额度视图ExampleExt方法
     *
     * @param lcCorpLmtView
     * @return
 */
public void buildExampleExt(LcCorpLmtViewQuery lcCorpLmtView,LcCorpLmtViewExample.Criteria criteria){

    //自定义实现
    }





}
