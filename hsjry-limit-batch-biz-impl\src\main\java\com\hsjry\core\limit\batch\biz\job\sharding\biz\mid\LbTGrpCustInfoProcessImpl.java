package com.hsjry.core.limit.batch.biz.job.sharding.biz.mid;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.hsjry.base.common.job.dto.IEnumTrade;
import com.hsjry.base.common.job.dto.JobInitDto;
import com.hsjry.base.common.job.dto.JobShared;
import com.hsjry.base.common.utils.AppParamUtil;
import com.hsjry.core.limit.batch.biz.job.JobCoreBusiness;
import com.hsjry.core.limit.batch.biz.job.entity.ShardingResult;
import com.hsjry.core.limit.batch.biz.job.sharding.biz.limit.AbstractShardingPrepareBiz;
import com.hsjry.core.limit.batch.common.enums.EnumBatchJobError;
import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;
import com.hsjry.core.limit.batch.dal.dao.intf.LbTGrpCustInfoDao;
import com.hsjry.core.limit.center.dal.dao.intf.CustLimitObjectInfoBatchDao;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitObjectInfoDo;
import com.hsjry.core.limit.center.dal.dao.model.LcSliceBatchSerialDo;
import com.hsjry.core.limit.center.dal.dao.query.CustLimitObjectInfoQuery;
import com.hsjry.lang.common.exception.HsjryBizException;
import com.hsjry.lang.common.utils.CollectionUtil;
import com.hsjry.lang.common.utils.GsonUtil;
import com.hsjry.lang.common.utils.StringUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 额度中心-中间表-集团客户信息处理
 * 执行truncate + insert SQL操作，将LC_CUST_LIMIT_OBJECT_INFO中的集团客户数据导入到LB_T_GRP_CUST_INFO
 *
 * <AUTHOR>
 * @date 2025-01-16
 */
@Slf4j
@Service("lbTGrpCustInfoProcessImpl")
@RequiredArgsConstructor
public class LbTGrpCustInfoProcessImpl extends AbstractShardingPrepareBiz<CustLimitObjectInfoQuery>
    implements JobCoreBusiness<LcCustLimitObjectInfoDo> {

    private final LbTGrpCustInfoDao lbTGrpCustInfoDao;
    private final CustLimitObjectInfoBatchDao custLimitObjectInfoBatchDao;

    @Override
    public IEnumTrade getJobTrade() {
        return EnumJobTrade.LB_T_GRP_CUST_INFO_PROCESS;
    }

    @Override
    public Integer selectCountByCurrentGroupFromDb(CustLimitObjectInfoQuery query) {
        return custLimitObjectInfoBatchDao.selectCountByCurrentGroup(query);
    }

    @Override
    public List<JobShared> generateJobSharding(JobInitDto jobInitDto) {
        Integer businessDate = jobInitDto.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobInitDto.getBatchSerialNo();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]查询处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);
        log.info(prefixLog + "====================== 分片逻辑 start ================================================");

        List<JobShared> jobSharedList = new ArrayList<>();
        // SQL批处理数量，暂定为分片数量
        Integer batchFixNum = jobInitDto.getFixNum();
        // 当前分组的最大值，为下次批处理的最小值
        LcCustLimitObjectInfoDo maxLimitInfoDo = new LcCustLimitObjectInfoDo();

        // 构造查询条件，查询当前分批处理的排序最大对象
        CustLimitObjectInfoQuery query = CustLimitObjectInfoQuery.builder().tenantId(AppParamUtil.getTenantId()).offset(
            batchFixNum - 1).limit(1).build();

        // 分片流水
        int batchNum = 0;
        while (maxLimitInfoDo != null) {
            query.setCustLimitId(maxLimitInfoDo.getCustLimitId());
            maxLimitInfoDo = custLimitObjectInfoBatchDao.selectFirstOne(query);
            // 统计分片数量
            batchNum = countBatchNum(batchFixNum, query, maxLimitInfoDo, batchNum, jobInitDto, jobSharedList,
                query.getCustLimitId(), false);
        }

        log.info(prefixLog + "====================== 分片逻辑 end ================================================");
        log.info(prefixLog + "额度实例所属对象信息同步分片任务生成完成,共{}个分片", jobSharedList.size());
        return jobSharedList;
    }

    @Override
    public ShardingResult<LcCustLimitObjectInfoDo> queryShardingResult(LcSliceBatchSerialDo lcSliceBatchSerialDo,
        JobInitDto jobInitDto, JobShared jobShared) {

        Integer businessDate = jobInitDto.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobShared.getBatchSerialNo();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]查询处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);

        log.info(prefixLog + "开始查询分片数据,分片号:{}", jobShared.getBatchNum());

        ShardingResult<LcCustLimitObjectInfoDo> shardingResult = new ShardingResult<>(lcSliceBatchSerialDo, jobShared);
        if (StringUtil.isBlank(jobShared.getExtParam())) {
            return shardingResult;
        }

        // 创建查询条件
        CustLimitObjectInfoQuery query = CustLimitObjectInfoQuery.builder().tenantId(AppParamUtil.getTenantId())//
            .offset(jobShared.getOffset()).limit(jobShared.getLimit()).build();

        log.info(prefixLog + "=========处理分片查询条件:" + GsonUtil.obj2Json(query) + "=========");

        // 查询分片数据
        List<LcCustLimitObjectInfoDo> dataList = custLimitObjectInfoBatchDao.selectShardList(query);
        shardingResult.setShardingResultList(dataList);

        log.info("分片数据查询完成,分片号:{},数据量:{}", jobShared.getBatchNum(),
            CollectionUtil.isEmpty(dataList) ? 0 : dataList.size());

        return shardingResult;
    }

    @Override
    public void execJobCoreBusiness(ShardingResult<LcCustLimitObjectInfoDo> shardingResult) {
        List<LcCustLimitObjectInfoDo> shardingDataList = shardingResult.getShardingResultList();
        JobShared jobShared = shardingResult.getJobShared();
        Integer businessDate = jobShared.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobShared.getBatchSerialNo();
        Integer batchNum = jobShared.getBatchNum();
        Integer dataSize = shardingResult.getShardingResultList().size();

        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]执行处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);
        log.info(prefixLog + "=========分片执行开始:[{}]数量为[{}]===========", batchNum, dataSize);

        // 判空处理，若分片数据为空直接返回
        if (CollectionUtil.isEmpty(shardingDataList)) {
            log.info(prefixLog + "=========分片执行结束:[{}]数量为空===========", batchNum);
            return;
        }

        try {
            LcSliceBatchSerialDo sliceBatchSerialDo = shardingResult.getLcSliceBatchSerialDo();
            // 只在第一个分片中清空目标表
            if (sliceBatchSerialDo.getBatchNum() == 1) {
                // 执行truncate操作
                log.info(prefixLog + "开始执行truncate table LB_T_GRP_CUST_INFO");
                int truncateResult = lbTGrpCustInfoDao.truncateTable();
                log.info(prefixLog + "truncate操作完成,影响行数:{}", truncateResult);
            }
            //查询[网贷系统-落地表-合同信息]中所有的[USER_ID]
            List<String> userIdList = shardingDataList.stream().filter(Objects::nonNull)//
                .map(LcCustLimitObjectInfoDo::getUserId)//
                .filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
            // 执行insert from source操作
            log.info(prefixLog + "开始从LC_CUST_LIMIT_OBJECT_INFO导入集团客户数据");
            int insertResult = lbTGrpCustInfoDao.insertFromSource(userIdList);
            log.info(prefixLog + "数据导入完成,成功插入{}条记录", insertResult);
            // 更新分片流水成功
            normalUpdateSliceSerial(insertResult, sliceBatchSerialDo);

        } catch (Exception e) {
            log.error(prefixLog + "分片执行异常:[{}]", batchNum, e);
            throw new HsjryBizException(EnumBatchJobError.SYSTEM_ERR.getCode(),
                EnumBatchJobError.SYSTEM_ERR.getDescription());
        }

        log.info(prefixLog + "=========分片执行结束:[{}]===========", batchNum);
    }
} 