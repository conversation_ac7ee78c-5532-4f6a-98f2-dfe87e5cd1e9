package com.hsjry.core.limit.batch.dal.dao.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.hsjry.core.limit.batch.dal.dao.model.LbTElcblIbnkProdLmtInfoDo;
import com.hsjry.lang.mybatis.mapper.common.CommonMapper;

/**
 * 电票系统-中间表-同业客户产品层额度信息mapper
 *
 * <AUTHOR>
 * @date 2025-07-28 11:16:05
 */
public interface LbTElcblIbnkProdLmtInfoMapper extends CommonMapper<LbTElcblIbnkProdLmtInfoDo> {
    /**
     * 将电票同业客户额度同步数据插入到产品层额度信息表
     * 执行复杂的多表关联查询，将结果插入到lb_t_elcbl_ibnk_prod_lmt_info表
     *
     * @param templateNodeIdList 模板节点ID列表
     * @param custLimitIdList 客户额度ID列表
     * @return int 插入的记录数
     */
    int insertElcblIbnkProdLmtInfo(@Param("templateNodeIdList") List<String> templateNodeIdList,
        @Param("custLimitIdList") List<String> custLimitIdList);

    /**
     * 更新额度实例金额信息中的实占额度
     * 将电票系统-中间表-同业客户产品层额度信息中的实占额度同步到额度实例金额信息表
     *
     * @param custLimitIdList 客户额度ID列表
     * @return int 更新的记录数
     */
    int updateRealOccupyAmount(@Param("custLimitIdList") List<String> custLimitIdList);

    /**
     * 删除电票系统-中间表-同业客户产品层额度信息表中的所有数据
     * 用于清空表数据，重新导入
     *
     * @return int 删除的记录数
     */
    int deleteAll();
}