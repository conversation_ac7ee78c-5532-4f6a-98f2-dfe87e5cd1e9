package com.hsjry.core.limit.batch.dal.dao.impl;

import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import com.hsjry.base.common.utils.AppParamUtil;
import com.hsjry.core.limit.batch.dal.dao.intf.LbTElcblIbnkProdLmtInfoDao;
import com.hsjry.core.limit.batch.dal.dao.mapper.LbTElcblIbnkProdLmtInfoMapper;
import com.hsjry.core.limit.batch.dal.dao.model.LbTElcblIbnkProdLmtInfoDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbTElcblIbnkProdLmtInfoExample;
import com.hsjry.core.limit.batch.dal.dao.model.LbTElcblIbnkProdLmtInfoKeyDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbTElcblIbnkProdLmtInfoQuery;
import com.hsjry.lang.business.date.BusinessDateUtil;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

import lombok.extern.slf4j.Slf4j;

/**
 * 电票系统-中间表-同业客户产品层额度信息数据库操作接口实现
 *
 * <AUTHOR>
 * @date 2025-07-28 11:16:05
 */
@Slf4j
@Repository
public class LbTElcblIbnkProdLmtInfoDaoImpl
    extends AbstractBaseDaoImpl<LbTElcblIbnkProdLmtInfoDo, LbTElcblIbnkProdLmtInfoMapper>
    implements LbTElcblIbnkProdLmtInfoDao {
    /**
     * 分页查询
     *
     * @param lbTElcblIbnkProdLmtInfo 条件
     * @param pageParam 分页参数
     * @return
     */
    @Override
    public PageInfo<LbTElcblIbnkProdLmtInfoDo> selectPage(LbTElcblIbnkProdLmtInfoQuery lbTElcblIbnkProdLmtInfo,
        PageParam pageParam) {
        LbTElcblIbnkProdLmtInfoExample example = buildExample(lbTElcblIbnkProdLmtInfo);
        return PageHelper.<LbTElcblIbnkProdLmtInfoDo>startPage(pageParam.getPageNum(), pageParam.getPageSize())
            .doSelectPageInfo(() -> getMapper().selectByExample(example));
    }

    /**
     * 根据key查询电票系统-中间表-同业客户产品层额度信息
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    @Override
    public LbTElcblIbnkProdLmtInfoDo selectByKey(String custNo, String custLimitId) {
        LbTElcblIbnkProdLmtInfoKeyDo lbTElcblIbnkProdLmtInfoKeyDo = new LbTElcblIbnkProdLmtInfoKeyDo();
        lbTElcblIbnkProdLmtInfoKeyDo.setCustNo(custNo);
        lbTElcblIbnkProdLmtInfoKeyDo.setCustLimitId(custLimitId);
        lbTElcblIbnkProdLmtInfoKeyDo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectByPrimaryKey(lbTElcblIbnkProdLmtInfoKeyDo);
    }

    /**
     * 根据key删除电票系统-中间表-同业客户产品层额度信息
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    @Override
    public int deleteByKey(String custNo, String custLimitId) {
        LbTElcblIbnkProdLmtInfoKeyDo lbTElcblIbnkProdLmtInfoKeyDo = new LbTElcblIbnkProdLmtInfoKeyDo();
        lbTElcblIbnkProdLmtInfoKeyDo.setCustNo(custNo);
        lbTElcblIbnkProdLmtInfoKeyDo.setCustLimitId(custLimitId);
        lbTElcblIbnkProdLmtInfoKeyDo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().deleteByPrimaryKey(lbTElcblIbnkProdLmtInfoKeyDo);
    }

    /**
     * 查询电票系统-中间表-同业客户产品层额度信息信息
     *
     * @param lbTElcblIbnkProdLmtInfo 条件
     * @return List<LbTElcblIbnkProdLmtInfoDo>
     */
    @Override
    public List<LbTElcblIbnkProdLmtInfoDo> selectByExample(LbTElcblIbnkProdLmtInfoQuery lbTElcblIbnkProdLmtInfo) {
        return getMapper().selectByExample(buildExample(lbTElcblIbnkProdLmtInfo));
    }

    /**
     * 新增电票系统-中间表-同业客户产品层额度信息信息
     *
     * @param lbTElcblIbnkProdLmtInfo 条件
     * @return int>
     */
    @Override
    public int insertBySelective(LbTElcblIbnkProdLmtInfoDo lbTElcblIbnkProdLmtInfo) {
        if (lbTElcblIbnkProdLmtInfo == null) {
            return -1;
        }

        lbTElcblIbnkProdLmtInfo.setCreateTime(BusinessDateUtil.getDate());
        lbTElcblIbnkProdLmtInfo.setUpdateTime(BusinessDateUtil.getDate());
        lbTElcblIbnkProdLmtInfo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().insertSelective(lbTElcblIbnkProdLmtInfo);
    }

    /**
     * 修改电票系统-中间表-同业客户产品层额度信息信息
     *
     * @param lbTElcblIbnkProdLmtInfo
     * @return
     */
    @Override
    public int updateBySelective(LbTElcblIbnkProdLmtInfoDo lbTElcblIbnkProdLmtInfo) {
        if (lbTElcblIbnkProdLmtInfo == null) {
            return -1;
        }
        lbTElcblIbnkProdLmtInfo.setUpdateTime(BusinessDateUtil.getDate());
        lbTElcblIbnkProdLmtInfo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().updateByPrimaryKeySelective(lbTElcblIbnkProdLmtInfo);
    }

    @Override
    public int updateBySelectiveByExample(LbTElcblIbnkProdLmtInfoDo lbTElcblIbnkProdLmtInfo,
        LbTElcblIbnkProdLmtInfoQuery lbTElcblIbnkProdLmtInfoQuery) {
        lbTElcblIbnkProdLmtInfo.setUpdateTime(BusinessDateUtil.getDate());
        return getMapper().updateByExampleSelective(lbTElcblIbnkProdLmtInfo,
            buildExample(lbTElcblIbnkProdLmtInfoQuery));
    }

    /**
     * 构建电票系统-中间表-同业客户产品层额度信息Example信息
     *
     * @param lbTElcblIbnkProdLmtInfo
     * @return
     */
    public LbTElcblIbnkProdLmtInfoExample buildExample(LbTElcblIbnkProdLmtInfoQuery lbTElcblIbnkProdLmtInfo) {
        LbTElcblIbnkProdLmtInfoExample example = new LbTElcblIbnkProdLmtInfoExample();
        LbTElcblIbnkProdLmtInfoExample.Criteria criteria = example.createCriteria();
        criteria.andTenantIdEqualTo(AppParamUtil.getTenantId());
        if (lbTElcblIbnkProdLmtInfo != null) {
            //添加查询条件
            if (StringUtil.isNotEmpty(lbTElcblIbnkProdLmtInfo.getCustLimitId())) {
                criteria.andCustLimitIdEqualTo(lbTElcblIbnkProdLmtInfo.getCustLimitId());
            }
            if (StringUtil.isNotEmpty(lbTElcblIbnkProdLmtInfo.getOwnOrganId())) {
                criteria.andOwnOrganIdEqualTo(lbTElcblIbnkProdLmtInfo.getOwnOrganId());
            }
            if (StringUtil.isNotEmpty(lbTElcblIbnkProdLmtInfo.getOperatorId())) {
                criteria.andOperatorIdEqualTo(lbTElcblIbnkProdLmtInfo.getOperatorId());
            }
            if (StringUtil.isNotEmpty(lbTElcblIbnkProdLmtInfo.getCoreInstNo())) {
                criteria.andCoreInstNoEqualTo(lbTElcblIbnkProdLmtInfo.getCoreInstNo());
            }
            if (null != lbTElcblIbnkProdLmtInfo.getRealOccupyAmount()) {
                criteria.andRealOccupyAmountEqualTo(lbTElcblIbnkProdLmtInfo.getRealOccupyAmount());
            }
            if (StringUtil.isNotEmpty(lbTElcblIbnkProdLmtInfo.getLimitStatus())) {
                criteria.andLimitStatusEqualTo(lbTElcblIbnkProdLmtInfo.getLimitStatus());
            }
            if (StringUtil.isNotEmpty(lbTElcblIbnkProdLmtInfo.getCustNo())) {
                criteria.andCustNoEqualTo(lbTElcblIbnkProdLmtInfo.getCustNo());
            }
            if (null != lbTElcblIbnkProdLmtInfo.getUseOccupyAmount()) {
                criteria.andUseOccupyAmountEqualTo(lbTElcblIbnkProdLmtInfo.getUseOccupyAmount());
            }
            if (null != lbTElcblIbnkProdLmtInfo.getAvailableAmount()) {
                criteria.andAvailableAmountEqualTo(lbTElcblIbnkProdLmtInfo.getAvailableAmount());
            }
            if (null != lbTElcblIbnkProdLmtInfo.getTotalAmount()) {
                criteria.andTotalAmountEqualTo(lbTElcblIbnkProdLmtInfo.getTotalAmount());
            }
            if (StringUtil.isNotEmpty(lbTElcblIbnkProdLmtInfo.getCertNo())) {
                criteria.andCertNoEqualTo(lbTElcblIbnkProdLmtInfo.getCertNo());
            }
            if (StringUtil.isNotEmpty(lbTElcblIbnkProdLmtInfo.getCertTyp())) {
                criteria.andCertTypEqualTo(lbTElcblIbnkProdLmtInfo.getCertTyp());
            }
            if (StringUtil.isNotEmpty(lbTElcblIbnkProdLmtInfo.getCustNm())) {
                criteria.andCustNmEqualTo(lbTElcblIbnkProdLmtInfo.getCustNm());
            }
            if (StringUtil.isNotEmpty(lbTElcblIbnkProdLmtInfo.getCustTyp())) {
                criteria.andCustTypEqualTo(lbTElcblIbnkProdLmtInfo.getCustTyp());
            }
        }
        buildExampleExt(lbTElcblIbnkProdLmtInfo, criteria);
        return example;
    }

    /**
     * 构建电票系统-中间表-同业客户产品层额度信息ExampleExt方法
     *
     * @param lbTElcblIbnkProdLmtInfo
     * @return
     */
    public void buildExampleExt(LbTElcblIbnkProdLmtInfoQuery lbTElcblIbnkProdLmtInfo,
        LbTElcblIbnkProdLmtInfoExample.Criteria criteria) {

        //自定义实现
    }

    /**
     * 将电票同业客户额度同步数据插入到产品层额度信息表
     * 执行复杂的多表关联查询，将结果插入到lb_t_elcbl_ibnk_prod_lmt_info表
     *
     * @param templateNodeIdList 模板节点ID列表
     * @param custLimitIdList 客户额度ID列表
     * @return int 插入的记录数
     */
    @Override
    public int insertElcblIbnkProdLmtInfo(List<String> templateNodeIdList, List<String> custLimitIdList) {
        if (CollectionUtils.isEmpty(templateNodeIdList) || CollectionUtils.isEmpty(custLimitIdList)) {
            log.warn("模板节点ID列表或客户额度ID列表为空，跳过插入操作");
            return 0;
        }
        return getMapper().insertElcblIbnkProdLmtInfo(templateNodeIdList, custLimitIdList);
    }

    /**
     * 更新额度实例金额信息中的实占额度
     * 将电票系统-中间表-同业客户产品层额度信息中的实占额度同步到额度实例金额信息表
     *
     * @param custLimitIdList 客户额度ID列表
     * @return int 更新的记录数
     */
    @Override
    public int updateRealOccupyAmount(List<String> custLimitIdList) {
        if (CollectionUtils.isEmpty(custLimitIdList)) {
            log.warn("客户额度ID列表为空，跳过更新实占额度操作");
            return 0;
        }
        return getMapper().updateRealOccupyAmount(custLimitIdList);
    }

    /**
     * 删除电票系统-中间表-同业客户产品层额度信息表中的所有数据
     * 用于清空表数据，重新导入
     *
     * @return int 删除的记录数
     */
    @Override
    public int deleteAll() {
        return getMapper().deleteAll();
    }
}
