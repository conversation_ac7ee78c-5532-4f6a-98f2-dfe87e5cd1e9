<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hsjry.core.limit.batch.dal.dao.mapper.LbSElcblIbnkLmtSynzMapper">
    <resultMap id="BaseResultMap" type="com.hsjry.core.limit.batch.dal.dao.model.LbSElcblIbnkLmtSynzDo">
        <result property="id" column="id" jdbcType="VARCHAR"/> <!-- 主键ID -->
        <result property="ibnkUserId" column="ibnk_user_id" jdbcType="VARCHAR"/> <!-- 同业客户编号 -->
        <result property="ibnkUserCertificateKind" column="ibnk_user_certificate_kind"
                jdbcType="VARCHAR"/> <!-- 同业客户证件类型 -->
        <result property="ibnkUserCertificateNo" column="ibnk_user_certificate_no"
                jdbcType="VARCHAR"/> <!-- 同业客户证件号码 -->
        <result property="totalAmount" column="total_amount" jdbcType="DECIMAL"/> <!-- 总额度 -->
        <result property="availableAmount" column="available_amount" jdbcType="DECIMAL"/> <!-- 可用额度 -->
        <result property="useOccupyAmount" column="use_occupy_amount" jdbcType="DECIMAL"/> <!-- 已占用额度 -->
        <result property="coreInstNo" column="core_inst_no" jdbcType="VARCHAR"/> <!-- 核心机构号 -->
        <result property="createTime" column="create_time" jdbcType="VARCHAR"/> <!-- 创建时间 -->
        <result property="updateTime" column="update_time" jdbcType="VARCHAR"/> <!-- 更新时间 -->
    </resultMap>
    <sql id="Base_Column_List">
        id
        , ibnk_user_id
        , ibnk_user_certificate_kind
                , ibnk_user_certificate_no
                , total_amount
                , available_amount
                , use_occupy_amount
                , core_inst_no
                , create_time
                , update_time
    </sql>
    <!-- 批量插入电票系统-落地表-同业客户额度同步信息 -->
    <insert id="insertList" parameterType="java.util.List">
        INSERT ALL
        <foreach collection="list" item="item" separator="">
            INTO lb_s_elcbl_ibnk_lmt_synz (
            id, ibnk_user_id, ibnk_user_certificate_kind, ibnk_user_certificate_no, total_amount, available_amount,
            use_occupy_amount, core_inst_no, create_time, update_time
            ) VALUES (
            #{item.id, jdbcType=VARCHAR},
            #{item.ibnkUserId, jdbcType=VARCHAR},
            #{item.ibnkUserCertificateKind, jdbcType=VARCHAR},
            #{item.ibnkUserCertificateNo, jdbcType=VARCHAR},
            #{item.totalAmount, jdbcType=DECIMAL},
            #{item.availableAmount, jdbcType=DECIMAL},
            #{item.useOccupyAmount, jdbcType=DECIMAL},
            #{item.coreInstNo, jdbcType=VARCHAR},
            #{item.createTime, jdbcType=VARCHAR},
            #{item.updateTime, jdbcType=VARCHAR}
            )
        </foreach>
        SELECT * FROM DUAL
    </insert>

    <!-- 清空电票系统-落地表-同业客户额度同步表所有数据 -->
    <delete id="deleteAll" parameterType="java.lang.String">
        TRUNCATE TABLE lb_s_elcbl_ibnk_lmt_synz
    </delete>

    <!-- 获取第一个对象，用于分片查询 -->
    <select id="selectFirstOne" resultMap="BaseResultMap" parameterType="com.hsjry.core.limit.batch.dal.dao.query.LbSElcblIbnkLmtSynzQuery">
        SELECT * FROM (
            SELECT
            <include refid="Base_Column_List"/>
            FROM lb_s_elcbl_ibnk_lmt_synz
            <where>
                <if test="query != null and query.ibnkUserId != null and query.ibnkUserId != ''">
                    AND ibnk_user_id &gt; #{query.ibnkUserId,jdbcType=VARCHAR}
                </if>
                <if test="query != null and query.ibnkUserCertificateKind != null and query.ibnkUserCertificateKind != ''">
                    AND ibnk_user_certificate_kind = #{query.ibnkUserCertificateKind,jdbcType=VARCHAR}
                </if>
                <if test="query != null and query.ibnkUserCertificateNo != null and query.ibnkUserCertificateNo != ''">
                    AND ibnk_user_certificate_no = #{query.ibnkUserCertificateNo,jdbcType=VARCHAR}
                </if>
                <if test="query != null and query.totalAmount != null">
                    AND total_amount = #{query.totalAmount,jdbcType=DECIMAL}
                </if>
                <if test="query != null and query.availableAmount != null">
                    AND available_amount = #{query.availableAmount,jdbcType=DECIMAL}
                </if>
                <if test="query != null and query.useOccupyAmount != null">
                    AND use_occupy_amount = #{query.useOccupyAmount,jdbcType=DECIMAL}
                </if>
                <if test="query != null and query.coreInstNo != null and query.coreInstNo != ''">
                    AND core_inst_no = #{query.coreInstNo,jdbcType=VARCHAR}
                </if>
            </where>
            ORDER BY ibnk_user_id ASC
        ) WHERE ROWNUM = 1
    </select>

    <!-- 查询当前分片主键范围内的数据总数 -->
    <select id="selectCountByCurrentGroup" resultType="java.lang.Integer" parameterType="com.hsjry.core.limit.batch.dal.dao.query.LbSElcblIbnkLmtSynzQuery">
        SELECT COUNT(1)
        FROM lb_s_elcbl_ibnk_lmt_synz
        <where>
            <if test="query != null and query.ibnkUserId != null and query.ibnkUserId != ''">
                AND ibnk_user_id &gt; #{query.ibnkUserId,jdbcType=VARCHAR}
            </if>
            <if test="query != null and query.ibnkUserCertificateKind != null and query.ibnkUserCertificateKind != ''">
                AND ibnk_user_certificate_kind = #{query.ibnkUserCertificateKind,jdbcType=VARCHAR}
            </if>
            <if test="query != null and query.ibnkUserCertificateNo != null and query.ibnkUserCertificateNo != ''">
                AND ibnk_user_certificate_no = #{query.ibnkUserCertificateNo,jdbcType=VARCHAR}
            </if>
            <if test="query != null and query.totalAmount != null">
                AND total_amount = #{query.totalAmount,jdbcType=DECIMAL}
            </if>
            <if test="query != null and query.availableAmount != null">
                AND available_amount = #{query.availableAmount,jdbcType=DECIMAL}
            </if>
            <if test="query != null and query.useOccupyAmount != null">
                AND use_occupy_amount = #{query.useOccupyAmount,jdbcType=DECIMAL}
            </if>
            <if test="query != null and query.coreInstNo != null and query.coreInstNo != ''">
                AND core_inst_no = #{query.coreInstNo,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

</mapper>