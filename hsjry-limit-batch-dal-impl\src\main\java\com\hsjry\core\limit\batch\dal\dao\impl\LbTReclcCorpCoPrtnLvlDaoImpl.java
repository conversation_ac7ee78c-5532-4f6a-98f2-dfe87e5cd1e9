package com.hsjry.core.limit.batch.dal.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.hsjry.core.limit.batch.dal.dao.intf.LbTReclcCorpCoPrtnLvlDao;
import com.hsjry.core.limit.batch.dal.dao.mapper.LbTReclcCorpCoPrtnLvlMapper;
import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcCorpCoPrtnLvlDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcCorpCoPrtnLvlExample;
import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcCorpCoPrtnLvlKeyDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbTReclcCorpCoPrtnLvlQuery;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度中心-中间表-额度重算中对公客户中合作方层额度数据库操作接口实现
 *
 * <AUTHOR>
 * @date 2025-08-21 11:31:28
 */
@Repository
public class LbTReclcCorpCoPrtnLvlDaoImpl
    extends AbstractBaseDaoImpl<LbTReclcCorpCoPrtnLvlDo, LbTReclcCorpCoPrtnLvlMapper>
    implements LbTReclcCorpCoPrtnLvlDao {
    /**
     * 分页查询
     *
     * @param lbTReclcCorpCoPrtnLvl 条件
     * @param pageParam 分页参数
     * @return
     */
    @Override
    public PageInfo<LbTReclcCorpCoPrtnLvlDo> selectPage(LbTReclcCorpCoPrtnLvlQuery lbTReclcCorpCoPrtnLvl,
        PageParam pageParam) {
        LbTReclcCorpCoPrtnLvlExample example = buildExample(lbTReclcCorpCoPrtnLvl);
        return PageHelper.<LbTReclcCorpCoPrtnLvlDo>startPage(pageParam.getPageNum(), pageParam.getPageSize())
            .doSelectPageInfo(() -> getMapper().selectByExample(example));
    }

    /**
     * 根据key查询额度中心-中间表-额度重算中对公客户中合作方层额度
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    @Override
    public LbTReclcCorpCoPrtnLvlDo selectByKey(String custNo, String custLimitId) {
        LbTReclcCorpCoPrtnLvlKeyDo lbTReclcCorpCoPrtnLvlKeyDo = new LbTReclcCorpCoPrtnLvlKeyDo();
        lbTReclcCorpCoPrtnLvlKeyDo.setCustNo(custNo);
        lbTReclcCorpCoPrtnLvlKeyDo.setCustLimitId(custLimitId);
        return getMapper().selectByPrimaryKey(lbTReclcCorpCoPrtnLvlKeyDo);
    }

    /**
     * 根据key删除额度中心-中间表-额度重算中对公客户中合作方层额度
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    @Override
    public int deleteByKey(String custNo, String custLimitId) {
        LbTReclcCorpCoPrtnLvlKeyDo lbTReclcCorpCoPrtnLvlKeyDo = new LbTReclcCorpCoPrtnLvlKeyDo();
        lbTReclcCorpCoPrtnLvlKeyDo.setCustNo(custNo);
        lbTReclcCorpCoPrtnLvlKeyDo.setCustLimitId(custLimitId);
        return getMapper().deleteByPrimaryKey(lbTReclcCorpCoPrtnLvlKeyDo);
    }

    /**
     * 查询额度中心-中间表-额度重算中对公客户中合作方层额度信息
     *
     * @param lbTReclcCorpCoPrtnLvl 条件
     * @return List<LbTReclcCorpCoPrtnLvlDo>
     */
    @Override
    public List<LbTReclcCorpCoPrtnLvlDo> selectByExample(LbTReclcCorpCoPrtnLvlQuery lbTReclcCorpCoPrtnLvl) {
        return getMapper().selectByExample(buildExample(lbTReclcCorpCoPrtnLvl));
    }

    /**
     * 新增额度中心-中间表-额度重算中对公客户中合作方层额度信息
     *
     * @param lbTReclcCorpCoPrtnLvl 条件
     * @return int>
     */
    @Override
    public int insertBySelective(LbTReclcCorpCoPrtnLvlDo lbTReclcCorpCoPrtnLvl) {
        if (lbTReclcCorpCoPrtnLvl == null) {
            return -1;
        }

        return getMapper().insertSelective(lbTReclcCorpCoPrtnLvl);
    }

    /**
     * 修改额度中心-中间表-额度重算中对公客户中合作方层额度信息
     *
     * @param lbTReclcCorpCoPrtnLvl
     * @return
     */
    @Override
    public int updateBySelective(LbTReclcCorpCoPrtnLvlDo lbTReclcCorpCoPrtnLvl) {
        if (lbTReclcCorpCoPrtnLvl == null) {
            return -1;
        }

        return getMapper().updateByPrimaryKeySelective(lbTReclcCorpCoPrtnLvl);
    }

    @Override
    public int updateBySelectiveByExample(LbTReclcCorpCoPrtnLvlDo lbTReclcCorpCoPrtnLvl,
        LbTReclcCorpCoPrtnLvlQuery lbTReclcCorpCoPrtnLvlQuery) {

        return getMapper().updateByExampleSelective(lbTReclcCorpCoPrtnLvl, buildExample(lbTReclcCorpCoPrtnLvlQuery));
    }

    /**
     * 构建额度中心-中间表-额度重算中对公客户中合作方层额度Example信息
     *
     * @param lbTReclcCorpCoPrtnLvl
     * @return
     */
    public LbTReclcCorpCoPrtnLvlExample buildExample(LbTReclcCorpCoPrtnLvlQuery lbTReclcCorpCoPrtnLvl) {
        LbTReclcCorpCoPrtnLvlExample example = new LbTReclcCorpCoPrtnLvlExample();
        LbTReclcCorpCoPrtnLvlExample.Criteria criteria = example.createCriteria();
        if (lbTReclcCorpCoPrtnLvl != null) {
            //添加查询条件
            if (StringUtil.isNotEmpty(lbTReclcCorpCoPrtnLvl.getCustNo())) {
                criteria.andCustNoEqualTo(lbTReclcCorpCoPrtnLvl.getCustNo());
            }
            if (StringUtil.isNotEmpty(lbTReclcCorpCoPrtnLvl.getCustLimitId())) {
                criteria.andCustLimitIdEqualTo(lbTReclcCorpCoPrtnLvl.getCustLimitId());
            }
            if (StringUtil.isNotEmpty(lbTReclcCorpCoPrtnLvl.getTemplateNodeId())) {
                criteria.andTemplateNodeIdEqualTo(lbTReclcCorpCoPrtnLvl.getTemplateNodeId());
            }
            if (StringUtil.isNotEmpty(lbTReclcCorpCoPrtnLvl.getLimitStatus())) {
                criteria.andLimitStatusEqualTo(lbTReclcCorpCoPrtnLvl.getLimitStatus());
            }
            if (null != lbTReclcCorpCoPrtnLvl.getTotalAmount()) {
                criteria.andTotalAmountEqualTo(lbTReclcCorpCoPrtnLvl.getTotalAmount());
            }
            if (null != lbTReclcCorpCoPrtnLvl.getPreOccupyAmount()) {
                criteria.andPreOccupyAmountEqualTo(lbTReclcCorpCoPrtnLvl.getPreOccupyAmount());
            }
            if (null != lbTReclcCorpCoPrtnLvl.getRealOccupyAmount()) {
                criteria.andRealOccupyAmountEqualTo(lbTReclcCorpCoPrtnLvl.getRealOccupyAmount());
            }
            if (null != lbTReclcCorpCoPrtnLvl.getLowRiskAmount()) {
                criteria.andLowRiskAmountEqualTo(lbTReclcCorpCoPrtnLvl.getLowRiskAmount());
            }
            if (null != lbTReclcCorpCoPrtnLvl.getPreOccupyLowRiskAmt()) {
                criteria.andPreOccupyLowRiskAmtEqualTo(lbTReclcCorpCoPrtnLvl.getPreOccupyLowRiskAmt());
            }
            if (null != lbTReclcCorpCoPrtnLvl.getRealOccupyLowRiskAmt()) {
                criteria.andRealOccupyLowRiskAmtEqualTo(lbTReclcCorpCoPrtnLvl.getRealOccupyLowRiskAmt());
            }
        }
        buildExampleExt(lbTReclcCorpCoPrtnLvl, criteria);
        return example;
    }

    /**
     * 构建额度中心-中间表-额度重算中对公客户中合作方层额度ExampleExt方法
     *
     * @param lbTReclcCorpCoPrtnLvl
     * @return
     */
    public void buildExampleExt(LbTReclcCorpCoPrtnLvlQuery lbTReclcCorpCoPrtnLvl,
        LbTReclcCorpCoPrtnLvlExample.Criteria criteria) {

        //自定义实现
    }

}
