package com.hsjry.core.limit.batch.biz.job.sharding.biz.mid;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.hsjry.base.common.job.dto.IEnumTrade;
import com.hsjry.base.common.job.dto.JobInitDto;
import com.hsjry.base.common.job.dto.JobShared;
import com.hsjry.base.common.model.enums.common.EnumCertificateKind;
import com.hsjry.base.common.model.enums.customer.EnumUserType;
import com.hsjry.base.common.model.enums.limit.EnumCustLimitStatus;
import com.hsjry.base.common.model.enums.limit.EnumLimitHandlerStatus;
import com.hsjry.base.common.model.enums.limit.EnumLmtTplNode;
import com.hsjry.core.limit.batch.biz.job.JobCoreBusiness;
import com.hsjry.core.limit.batch.biz.job.entity.ShardingResult;
import com.hsjry.core.limit.batch.biz.job.sharding.biz.limit.AbstractShardingPrepareBiz;
import com.hsjry.core.limit.batch.common.enums.EnumBatchJobError;
import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;
import com.hsjry.core.limit.batch.dal.dao.intf.LbSElcblIbnkLmtSynzDao;
import com.hsjry.core.limit.batch.dal.dao.intf.LbTElcblIbnkProdLmtInfoDao;
import com.hsjry.core.limit.batch.dal.dao.model.LbSElcblIbnkLmtSynzDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbSElcblIbnkLmtSynzQuery;
import com.hsjry.core.limit.center.dal.dao.intf.LcCustLimitInfoDao;
import com.hsjry.core.limit.center.dal.dao.intf.LcCustLimitObjectInfoDao;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitInfoDo;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitObjectInfoDo;
import com.hsjry.core.limit.center.dal.dao.model.LcSliceBatchSerialDo;
import com.hsjry.core.limit.center.dal.dao.query.LcCustLimitInfoQuery;
import com.hsjry.core.limit.center.dal.dao.query.LcCustLimitObjectInfoQuery;
import com.hsjry.lang.common.exception.HsjryBizException;
import com.hsjry.lang.common.utils.CollectionUtil;
import com.hsjry.lang.common.utils.GsonUtil;
import com.hsjry.lang.common.utils.StringUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 电票同业额度处理
 * 从源表同步数据到目标表，实现电票同业额度信息的处理
 *
 * <AUTHOR>
 * @version V3.0.5
 * @since 2025/7/10 12:31
 */
@Slf4j
@Service("lbTElcblIbnkProdLmtInfoProcessImpl")
@RequiredArgsConstructor
public class LbTElcblIbnkProdLmtInfoProcessImpl extends AbstractShardingPrepareBiz<LbSElcblIbnkLmtSynzQuery>
    implements JobCoreBusiness<LbSElcblIbnkLmtSynzDo> {
    private final LcCustLimitInfoDao custLimitInfoDao;
    private final LcCustLimitObjectInfoDao custLimitObjectInfoDao;
    private final LbSElcblIbnkLmtSynzDao lbSElcblIbnkLmtSynzDao;
    private final LbTElcblIbnkProdLmtInfoDao lbTElcblIbnkProdLmtInfoDao;

    @Override
    public IEnumTrade getJobTrade() {
        return EnumJobTrade.LB_T_ELCBL_IBNK_PROD_LMT_INFO_PROCESS;
    }

    /**
     * 查询当前分片主键范围内的数据总数
     * 根据ibnkUserId主键范围统计当前分片的数据量
     *
     * @param query 查询条件，需包含ibnkUserId范围
     * @return 当前分片的数据量
     */
    @Override
    public Integer selectCountByCurrentGroupFromDb(LbSElcblIbnkLmtSynzQuery query) {
        Integer count = lbSElcblIbnkLmtSynzDao.selectCountByCurrentGroup(query);
        return count != null ? count : 0;
    }

    /**
     * 生成分片任务列表
     * 采用ibnkUserId主键分页方式，循环查找最大ibnkUserId对象，动态生成分片任务
     *
     * @param jobInitDto 任务初始化参数
     * @return 分片任务列表
     */
    @Override
    public List<JobShared> generateJobSharding(JobInitDto jobInitDto) {
        Integer businessDate = jobInitDto.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobInitDto.getBatchSerialNo();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]查询处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);
        log.info(prefixLog + "====================== 分片逻辑 start ================================================");

        List<JobShared> jobSharedList = new ArrayList<>();
        // SQL批处理数量,暂定为分片数量
        Integer batchFixNum = jobInitDto.getFixNum();
        // 当前分组的最大值,为下次批处理的最小值
        LbSElcblIbnkLmtSynzDo maxDo = null;

        // 构造查询条件,查询当前分批处理的排序最大对象
        LbSElcblIbnkLmtSynzQuery query = LbSElcblIbnkLmtSynzQuery.builder().build();

        // 分片流水
        int batchNum = 0;
        int loopCount = 0;
        final int MAX_LOOP_COUNT = 10000; // 防止无限循环

        // 🔧 修复：添加已处理记录跟踪，防止重复处理相同的ibnkUserId
        Set<String> processedIbnkUserIds = new HashSet<>();
        String lastProcessedIbnkUserId = null;

        while (true) {
            // 防止无限循环
            if (loopCount++ > MAX_LOOP_COUNT) {
                log.error(prefixLog + "分片生成循环次数超过最大限制[{}],强制退出。已处理的ibnkUserId数量:[{}]，最后处理的ibnkUserId:[{}]",
                    MAX_LOOP_COUNT, processedIbnkUserIds.size(), lastProcessedIbnkUserId);
                throw new HsjryBizException(EnumBatchJobError.SYSTEM_ERR.getCode(),
                    "分片生成循环次数超过最大限制，可能存在数据问题或逻辑错误。已处理记录数:" + processedIbnkUserIds.size());
            }

            if (maxDo != null) {
                query.setIbnkUserId(maxDo.getIbnkUserId());
                log.debug(prefixLog + "当前查询ibnkUserId大于[{}]", maxDo.getIbnkUserId());
            } else {
                log.debug(prefixLog + "首次查询，获取最小ibnkUserId记录");
            }

            try {
                maxDo = lbSElcblIbnkLmtSynzDao.selectFirstOne(query);
            } catch (Exception e) {
                log.error(prefixLog + "查询分片数据失败，循环次数[{}]", loopCount, e);
                throw new HsjryBizException(EnumBatchJobError.SYSTEM_ERR.getCode(),
                    "查询分片数据失败: " + e.getMessage());
            }

            if (maxDo == null) {
                log.info(prefixLog + "未找到更多数据，分片生成完成，循环次数[{}]，总共处理了[{}]个不同的ibnkUserId",
                    loopCount - 1, processedIbnkUserIds.size());
                break;
            }

            // 🔧 修复：检查是否已经处理过相同的ibnkUserId，防止无限循环
            String currentIbnkUserId = maxDo.getIbnkUserId();
            if (processedIbnkUserIds.contains(currentIbnkUserId)) {
                log.error(prefixLog + "检测到重复的ibnkUserId:[{}]，可能存在数据质量问题，强制退出。循环次数:[{}]",
                    currentIbnkUserId, loopCount);
                throw new HsjryBizException(EnumBatchJobError.SYSTEM_ERR.getCode(),
                    "检测到重复的ibnkUserId: " + currentIbnkUserId + "，可能存在数据质量问题");
            }

            // 记录已处理的ibnkUserId
            processedIbnkUserIds.add(currentIbnkUserId);
            lastProcessedIbnkUserId = currentIbnkUserId;

            log.debug(prefixLog + "找到分片数据，ibnkUserId=[{}],循环次数[{}]，已处理不同ibnkUserId数量:[{}]",
                currentIbnkUserId, loopCount - 1, processedIbnkUserIds.size());

            // 统计分片数量
            batchNum = countBatchNum(batchFixNum, query, maxDo, batchNum, jobInitDto, jobSharedList,
                query.getIbnkUserId(), false);
        }

        log.info(prefixLog + "====================== 分片逻辑 end ================================================");
        log.info(prefixLog + "电票同业额度处理分片任务生成完成,共[{}]个分片，处理了[{}]个不同的ibnkUserId",
            jobSharedList.size(), processedIbnkUserIds.size());
        return jobSharedList;
    }

    @Override
    public ShardingResult<LbSElcblIbnkLmtSynzDo> queryShardingResult(LcSliceBatchSerialDo lcSliceBatchSerialDo,
        JobInitDto jobInitDto, JobShared jobShared) {
        Integer businessDate = jobInitDto.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobShared.getBatchSerialNo();
        Integer batchNum = jobShared.getBatchNum();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]查询处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);

        log.info(prefixLog + "开始查询分片数据,分片号:[{}]", batchNum);

        ShardingResult<LbSElcblIbnkLmtSynzDo> shardingResult = new ShardingResult<>(lcSliceBatchSerialDo, jobShared);
        if (StringUtil.isBlank(jobShared.getExtParam())) {
            log.warn(prefixLog + "分片扩展参数为空，跳过数据查询,分片号:[{}]", batchNum);
            return shardingResult;
        }

        // 创建查询条件
        LbSElcblIbnkLmtSynzQuery query = LbSElcblIbnkLmtSynzQuery.builder().build();

        log.debug(prefixLog + "分片查询条件:[{}]", GsonUtil.obj2Json(query));

        // 性能监控
        long startTime = System.currentTimeMillis();
        try {
            // 查询分片数据
            List<LbSElcblIbnkLmtSynzDo> dataList = lbSElcblIbnkLmtSynzDao.selectByExample(query);
            shardingResult.setShardingResultList(dataList);

            long endTime = System.currentTimeMillis();
            log.info(prefixLog + "分片数据查询完成,分片号:[{}],数据量:[{}],耗时:[{}]ms",
                batchNum, CollectionUtil.isEmpty(dataList) ? 0 : dataList.size(), (endTime - startTime));

            // 性能告警
            if (dataList != null && dataList.size() > jobInitDto.getFixNum() * 2) {
                log.warn(prefixLog + "分片数据量过大,分片号:[{}],数据量:[{}],可能影响性能",
                    batchNum, dataList.size());
            }

        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error(prefixLog + "分片数据查询异常,分片号:[{}],耗时:[{}]ms",
                batchNum, (endTime - startTime), e);
            throw new HsjryBizException(EnumBatchJobError.SYSTEM_ERR.getCode(),
                "分片数据查询失败: " + e.getMessage());
        }

        return shardingResult;
    }

    @Override
    public void execJobCoreBusiness(ShardingResult<LbSElcblIbnkLmtSynzDo> shardingResult) {
        JobShared jobShared = shardingResult.getJobShared();
        Integer businessDate = jobShared.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobShared.getBatchSerialNo();
        Integer batchNum = jobShared.getBatchNum();
        Integer dataSize = shardingResult.getShardingResultList().size();

        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]执行处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);

        List<LbSElcblIbnkLmtSynzDo> shardingDataList = shardingResult.getShardingResultList();

        if (CollectionUtil.isEmpty(shardingDataList)) {
            log.info(prefixLog + "=========分片执行结束:[{}]数量为空===========", batchNum);
            return;
        }

        log.info(prefixLog + "=========分片执行开始:[{}]数量为[{}]===========", batchNum, dataSize);
        try {
            LcSliceBatchSerialDo sliceBatchSerialDo = shardingResult.getLcSliceBatchSerialDo();

            // 只在第一个分片中清空目标表
            if (sliceBatchSerialDo.getBatchNum() == 1) {
                log.info(prefixLog + "第一个分片,开始清空目标表lb_t_elcbl_ibnk_prod_lmt_info");
                lbTElcblIbnkProdLmtInfoDao.deleteAll();
                log.info(prefixLog + "目标表lb_t_elcbl_ibnk_prod_lmt_info清空完成");
            }

            // 更新分片流水前，初始化执行状态，确保不为空
            if (sliceBatchSerialDo.getSharedStatus() == null) {
                sliceBatchSerialDo.setSharedStatus(EnumLimitHandlerStatus.IN_HANDLE.getCode());
            }

            // 数据转换和插入
            List<String> ibnkCertNoList = shardingDataList.stream().filter(Objects::nonNull)
                .map(LbSElcblIbnkLmtSynzDo::getIbnkUserCertificateNo)
                .filter(StringUtil::isNotEmpty).distinct().collect(Collectors.toList());
            List<String> coreInstNoList = shardingDataList.stream().filter(Objects::nonNull)
                .map(LbSElcblIbnkLmtSynzDo::getCoreInstNo)
                .filter(StringUtil::isNotEmpty).distinct().collect(Collectors.toList());

            // 查询额度实例所属对象信息
            List<LcCustLimitObjectInfoDo> objectInfoDoList = custLimitObjectInfoDao.selectByExample(
                LcCustLimitObjectInfoQuery.builder()
                    .userType(EnumUserType.INTERBANK_CUSTOMER.getCode())
                    .userCertificateKind(EnumCertificateKind.SOCIAL_CREDIT_CODE.getCode())
                    .userCertificateNoList(ibnkCertNoList)
                    .build());

            List<String> custLimitIdList = objectInfoDoList.stream().filter(Objects::nonNull)
                .map(LcCustLimitObjectInfoDo::getCustLimitId)
                .filter(StringUtil::isNotEmpty).distinct().collect(Collectors.toList());
            List<String> userIdList = objectInfoDoList.stream().filter(Objects::nonNull)
                .map(LcCustLimitObjectInfoDo::getUserId)
                .filter(StringUtil::isNotEmpty).distinct().collect(Collectors.toList());

            // 查询额度实例信息
            List<String> templateNodeIdList = Lists.newArrayList(EnumLmtTplNode.TYPJYWED.getTemplateNodeId());
            List<LcCustLimitInfoDo> limitInfoDoList = custLimitInfoDao.selectByExample(LcCustLimitInfoQuery.builder()
                .custLimitIdList(custLimitIdList)
                .limitObjectIdList(userIdList)
                .templateNodeIdList(templateNodeIdList)
                .blngLglpsnCoreInsNoList(coreInstNoList)
                .statusList(EnumCustLimitStatus.getOperableCodeList())
                .build());

            List<String> limitId4typjywedList = limitInfoDoList.stream().filter(Objects::nonNull)
                .map(LcCustLimitInfoDo::getCustLimitId)
                .filter(StringUtil::isNotEmpty).distinct().collect(Collectors.toList());

            // 往目标表插入数据
            if (CollectionUtil.isNotEmpty(limitId4typjywedList)) {
                int insertResult = lbTElcblIbnkProdLmtInfoDao.insertElcblIbnkProdLmtInfo(templateNodeIdList,
                    limitId4typjywedList);
                log.info(prefixLog + "往[电票系统-中间表-同业客户产品层额度信息]插入数据结果为[{}]", insertResult);
            }

            // 更新额度实例金额信息中的实占额度
            int updateResult = lbTElcblIbnkProdLmtInfoDao.updateRealOccupyAmount(limitId4typjywedList);
            log.info(prefixLog + "更新[额度实例金额信息]中[实占额度]结果为[{}]", updateResult);

            // 更新分片流水成功
            normalUpdateSliceSerial(dataSize, sliceBatchSerialDo);

        } catch (Exception e) {
            log.error(prefixLog + "分片执行异常:[{}]", batchNum, e);
            throw new HsjryBizException(EnumBatchJobError.SYSTEM_ERR.getCode(),
                EnumBatchJobError.SYSTEM_ERR.getDescription());
        }
        log.info(prefixLog + "=========分片执行结束:[{}]===========", batchNum);
    }
}