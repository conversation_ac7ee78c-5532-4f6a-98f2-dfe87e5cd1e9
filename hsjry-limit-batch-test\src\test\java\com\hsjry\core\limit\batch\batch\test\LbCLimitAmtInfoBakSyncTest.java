package com.hsjry.core.limit.batch.batch.test;

import java.util.List;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.hsjry.base.common.job.dto.JobInitDto;
import com.hsjry.base.common.job.dto.JobShared;
import com.hsjry.core.limit.batch.batch.test.base.AbstractBaseTest;
import com.hsjry.core.limit.batch.biz.job.sharding.biz.copy.LbCLimitAmtInfoBakSyncImpl;
import com.hsjry.core.limit.center.dal.dao.query.CustLimitAmtInfoQuery;
import com.hsjry.lang.common.utils.GsonUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * LbCLimitAmtInfoBakSync分片逻辑测试
 * 
 * <AUTHOR>
 * @date 2025/1/27
 */
@Slf4j
public class LbCLimitAmtInfoBakSyncTest extends AbstractBaseTest {

    @Autowired
    private LbCLimitAmtInfoBakSyncImpl lbCLimitAmtInfoBakSync;

    @Test
    public void testGenerateJobSharding() {
        // 准备测试数据
        JobInitDto jobInitDto = new JobInitDto();
        jobInitDto.setBusinessDate(20250127);
        jobInitDto.setBatchSerialNo("TEST_BATCH_001");
        jobInitDto.setFixNum(1000); // 每个分片1000条数据
        jobInitDto.setInPara("{}");

        // 执行分片生成
        List<JobShared> jobSharedList = lbCLimitAmtInfoBakSync.generateJobSharding(jobInitDto);

        // 验证分片结果
        log.info("生成的分片数量: {}", jobSharedList.size());
        
        for (int i = 0; i < jobSharedList.size(); i++) {
            JobShared jobShared = jobSharedList.get(i);
            log.info("分片[{}]: batchNum={}, offset={}, limit={}", 
                i + 1, jobShared.getBatchNum(), jobShared.getOffset(), jobShared.getLimit());
            
            // 验证分片参数
            assert jobShared.getBatchNum() == i + 1;
            assert jobShared.getOffset() == i * 1000;
            assert jobShared.getLimit() <= 1000;
            
            // 验证extParam不为空
            assert jobShared.getExtParam() != null && !jobShared.getExtParam().isEmpty();
            
            // 解析并验证查询条件
            CustLimitAmtInfoQuery query = GsonUtil.json2Obj(jobShared.getExtParam(), CustLimitAmtInfoQuery.class);
            assert query != null;
            assert query.getOffset() == jobShared.getOffset();
            assert query.getLimit() == jobShared.getLimit();
            
            log.info("分片[{}]查询条件: {}", i + 1, GsonUtil.obj2Json(query));
        }
        
        // 验证分片之间没有重叠
        for (int i = 0; i < jobSharedList.size() - 1; i++) {
            JobShared current = jobSharedList.get(i);
            JobShared next = jobSharedList.get(i + 1);
            
            // 当前分片的结束位置应该等于下一个分片的开始位置
            int currentEnd = current.getOffset() + current.getLimit();
            int nextStart = next.getOffset();
            
            log.info("分片[{}]结束位置: {}, 分片[{}]开始位置: {}", 
                i + 1, currentEnd, i + 2, nextStart);
            
            assert currentEnd == nextStart : String.format(
                "分片[%d]结束位置(%d)不等于分片[%d]开始位置(%d)", 
                i + 1, currentEnd, i + 2, nextStart);
        }
        
        log.info("分片生成逻辑测试通过！");
    }
}
