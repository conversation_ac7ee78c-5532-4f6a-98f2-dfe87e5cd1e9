<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hsjry.core.limit.center.dal.dao.mapper.LcCorpLmtViewMapper">
    <resultMap id="BaseResultMap" type="com.hsjry.core.limit.center.dal.dao.model.LcCorpLmtViewDo">
        <result property="comCrdtTerm" column="com_crdt_term" jdbcType="INTEGER"/> <!-- 一般授信额度期限 -->
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/> <!-- 更新时间 -->
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/> <!-- 创建时间 -->
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/> <!-- 租户号 -->
        <result property="ownOrganId" column="own_organ_id" jdbcType="VARCHAR"/> <!-- 所属组织编号 -->
        <result property="operatorId" column="operator_id" jdbcType="VARCHAR"/> <!-- 操作人编号 -->
        <result property="coPrtnAvlLmt" column="co_prtn_avl_lmt" jdbcType="DECIMAL"/> <!-- 合作方可用额度(元) -->
        <result property="coPrtnLmt" column="co_prtn_lmt" jdbcType="DECIMAL"/> <!-- 合作方额度(元) -->
        <result property="guarLmt" column="guar_lmt" jdbcType="DECIMAL"/> <!-- 担保额度(元) -->
        <result property="noCrdtLmt" column="no_crdt_lmt" jdbcType="DECIMAL"/> <!-- 非授信额度(元) -->
        <result property="whlLowRiskCrdtTermUnit" column="whl_low_risk_crdt_term_unit"
                jdbcType="VARCHAR"/> <!-- 纯低风险额度期限单位 -->
        <result property="whlLowRiskCrdtTerm" column="whl_low_risk_crdt_term" jdbcType="INTEGER"/> <!-- 纯低风险额度期限 -->
        <result property="whlLowRiskAvlLmt" column="whl_low_risk_avl_lmt" jdbcType="DECIMAL"/> <!-- 纯低风险可用额度(元) -->
        <result property="whlLowRiskLmt" column="whl_low_risk_lmt" jdbcType="DECIMAL"/> <!-- 纯低风险额度(元) -->
        <result property="comCrdtTermUnit" column="com_crdt_term_unit" jdbcType="VARCHAR"/> <!-- 一般授信额度期限单位 -->
        <result property="lcCorpLmtViewId" column="lc_corp_lmt_view_id" jdbcType="VARCHAR"/> <!-- 对公客户额度视图主键 -->
        <result property="lowRiskSubItmAvlLmt" column="low_risk_sub_itm_avl_lmt"
                jdbcType="DECIMAL"/> <!-- 低风险分项可用额度(元) -->
        <result property="lowRiskSubItmLmt" column="low_risk_sub_itm_lmt" jdbcType="DECIMAL"/> <!-- 低风险分项额度(元) -->
        <result property="esrAvlLmt" column="esr_avl_lmt" jdbcType="DECIMAL"/> <!-- 敞口可用额度(元) -->
        <result property="esrLmt" column="esr_lmt" jdbcType="DECIMAL"/> <!-- 敞口额度(元) -->
        <result property="totlCrdtLmt" column="totl_crdt_lmt" jdbcType="DECIMAL"/> <!-- 总额度(元) -->
        <result property="belgGrpNo" column="belg_grp_no" jdbcType="VARCHAR"/> <!-- 所属集团编号 -->
        <result property="belgGrpNm" column="belg_grp_nm" jdbcType="VARCHAR"/> <!-- 所属集团名称 -->
        <result property="belgIndsTyp" column="belg_inds_typ" jdbcType="VARCHAR"/> <!-- 所属行业类型 -->
        <result property="certNo" column="cert_no" jdbcType="VARCHAR"/> <!-- 证件号码 -->
        <result property="certTyp" column="cert_typ" jdbcType="VARCHAR"/> <!-- 证件类型 -->
        <result property="custNo" column="cust_no" jdbcType="VARCHAR"/> <!-- 客户编号 -->
        <result property="custNm" column="cust_nm" jdbcType="VARCHAR"/> <!-- 客户名称 -->
        <result property="custRiskTag" column="cust_risk_tag" jdbcType="VARCHAR"/> <!-- 客户风险标签 -->
    </resultMap>
    <sql id="Base_Column_List">
        com_crdt_term
        , update_time
                , create_time
                , tenant_id
                , own_organ_id
                , operator_id
                , co_prtn_avl_lmt
                , co_prtn_lmt
                , guar_lmt
                , no_crdt_lmt
                , whl_low_risk_crdt_term_unit
                , whl_low_risk_crdt_term
                , whl_low_risk_avl_lmt
                , whl_low_risk_lmt
                , com_crdt_term_unit
                , lc_corp_lmt_view_id
                , low_risk_sub_itm_avl_lmt
                , low_risk_sub_itm_lmt
                , esr_avl_lmt
                , esr_lmt
                , totl_crdt_lmt
                , belg_grp_no
                , belg_grp_nm
                , belg_inds_typ
                , cert_no
                , cert_typ
                , cust_no
                , cust_nm
                , cust_risk_tag
    </sql>

    <!-- 1.处理客户信息,使用MERGE语句实现存在则更新,不存在则插入 -->
    <update id="mergeCustomerInfo">
        merge into lc_corp_lmt_view tgt
        using (select ltcci.tenant_id,
                      ltcci.cust_no,
                      ltcci.cust_nm,
                      ltcci.cert_typ,
                      ltcci.cert_no
               from lb_t_corp_cust_info ltcci
               where 1 = 1) src
        on (
            tgt.tenant_id = src.tenant_id
                and tgt.cust_no = src.cust_no
            )
        when matched then
        update
            set tgt.cust_nm = src.cust_nm,
            tgt.cert_typ = src.cert_typ,
            tgt.cert_no = src.cert_no
            when not matched then
        insert (lc_corp_lmt_view_id, cust_nm, cust_no, cert_typ, cert_no,
            tenant_id)
            values
        (generate_primary_key('LCLV'),
            src.cust_nm,
            src.cust_no,
            src.cert_typ,
            src.cert_no,
            src.tenant_id)
    </update>

    <!-- 2.更新总额度/敞口额度/敞口可用额度 -->
    <update id="mergeTotalCreditLimit">
        merge into lc_corp_lmt_view tgt
        using (select lcli.tenant_id                                                          as tenant_id,
                      lcli.limit_object_id                                                    as cust_no,
                      lcli.cust_limit_id,
                      lcli.template_node_id,
                      lclai.total_amount                                                      as totl_crdt_lmt,
                      lclai.total_amount - lclai.low_risk_amount                              as esr_lmt,
                      lclai.total_amount - lclai.pre_occupy_amount - lclai.real_occupy_amount as esr_avl_lmt,
                      lcli.operator_id                                                        as operator_id,
                      lcli.own_organ_id                                                       as own_organ_id,
                      lcli.create_time                                                        as create_time
               from lc_cust_limit_info lcli
                        inner join lc_cust_limit_amt_info lclai
                                   on lcli.tenant_id = lclai.tenant_id
                                       and lcli.cust_limit_id = lclai.cust_limit_id
                                       and lcli.limit_object_id = lclai.cust_no
               where lcli.limit_template_id in ('HNNSDGKHEDTX')
                 and lcli.template_node_id in ('DGZED')) src
        on (
            tgt.tenant_id = src.tenant_id
                and tgt.cust_no = src.cust_no
            )
        when matched then
        update
            set tgt.totl_crdt_lmt = src.totl_crdt_lmt,
            tgt.esr_lmt = src.esr_lmt,
            tgt.esr_avl_lmt = src.esr_avl_lmt,
            tgt.operator_id = src.operator_id,
            tgt.own_organ_id = src.own_organ_id,
            tgt.create_time = src.create_time,
            tgt.update_time = sysdate
    </update>

    <!-- 3.更新低风险分项额度/低风险分项可用额度 -->
    <update id="mergeLowRiskSubItemLimit">
        merge into lc_corp_lmt_view tgt
        using (select lcli.tenant_id                                                                         as tenant_id,
               lcli.limit_object_id                                                                   as cust_no,
               lcli.cust_limit_id,
               lcli.template_node_id,
               lclai.low_risk_amount                                                                  as low_risk_sub_itm_lmt,
               lclai.low_risk_amount - lclai.pre_occupy_low_risk_amt -
               lclai.real_occupy_low_risk_amt                                                         as low_risk_sub_itm_avl_lmt
        from lc_cust_limit_info lcli
                 inner join lc_cust_limit_amt_info lclai
                            on lcli.tenant_id = lclai.tenant_id
                                and lcli.cust_limit_id = lclai.cust_limit_id
                                and lcli.limit_object_id = lclai.cust_no
        where lcli.limit_template_id in ('HNNSDGKHEDTX')
          and lcli.template_node_id in ('DGDFXFXED')) src
        on (
            tgt.tenant_id = src.tenant_id
                and tgt.cust_no = src.cust_no
            )
        when matched then
        update
            set tgt.low_risk_sub_itm_lmt = src.low_risk_sub_itm_lmt,
            tgt.low_risk_sub_itm_avl_lmt = src.low_risk_sub_itm_avl_lmt,
            tgt.update_time = sysdate
    </update>

    <!-- 4.更新一般授信额度期限/一般授信额度期限单位 -->
    <update id="mergeCommonCreditTerm">
        merge into lc_corp_lmt_view tgt
        using (select lcli.tenant_id       as tenant_id,
               lcli.limit_object_id as cust_no,
               lcli.cust_limit_id,
               lcli.template_node_id,
               lcli.limit_term      as com_crdt_term,
               lcli.limit_term_unit as com_crdt_term_unit
        from lc_cust_limit_info lcli
        where lcli.limit_template_id in ('HNNSDGKHEDTX')
          and lcli.template_node_id in ('DGYBSXED')) src
        on (
            tgt.tenant_id = src.tenant_id
                and tgt.cust_no = src.cust_no
            )
        when matched then
        update
            set tgt.com_crdt_term = src.com_crdt_term,
            tgt.com_crdt_term_unit = src.com_crdt_term_unit,
            tgt.update_time = sysdate
    </update>

    <!-- 5.更新纯低风险额度/纯低风险可用额度/纯低风险额度期限/纯低风险额度期限单位 -->
    <update id="mergeWholeLowRiskLimit">
        merge into lc_corp_lmt_view tgt
        using (select lcli.tenant_id                 as tenant_id,
                      lcli.limit_object_id           as cust_no,
                      lcli.cust_limit_id,
                      lcli.template_node_id,
                      lclai.low_risk_amount          as whl_low_risk_lmt,
                      lclai.low_risk_amount - lclai.pre_occupy_low_risk_amt -
                      lclai.real_occupy_low_risk_amt as whl_low_risk_avl_lmt,
                      lcli.limit_term                as whl_low_risk_crdt_term,
                      lcli.limit_term_unit           as whl_low_risk_crdt_term_unit
               from lc_cust_limit_info lcli
                        inner join lc_cust_limit_amt_info lclai
                                   on lcli.tenant_id = lclai.tenant_id
                                       and lcli.cust_limit_id = lclai.cust_limit_id
                                       and lcli.limit_object_id = lclai.cust_no
               where lcli.limit_template_id in ('HNNSDGKHEDTX')
                 and lcli.template_node_id in ('DGCDFXED')) src
        on (
            tgt.tenant_id = src.tenant_id
                and tgt.cust_no = src.cust_no
            )
        when matched then
        update
            set tgt.whl_low_risk_lmt = src.whl_low_risk_lmt,
            tgt.whl_low_risk_avl_lmt = src.whl_low_risk_avl_lmt,
            tgt.whl_low_risk_crdt_term = src.whl_low_risk_crdt_term,
            tgt.whl_low_risk_crdt_term_unit = src.whl_low_risk_crdt_term_unit,
            tgt.update_time = sysdate
    </update>

    <!-- 6.更新非授信额度 -->
    <update id="mergeNoCreditLimit">
        merge into lc_corp_lmt_view tgt
        using (select lcli.tenant_id       as tenant_id,
                      lcli.limit_object_id as cust_no,
                      lcli.cust_limit_id,
                      lcli.template_node_id,
                      lclai.total_amount   as no_crdt_lmt
               from lc_cust_limit_info lcli
                        inner join lc_cust_limit_amt_info lclai
                                   on lcli.tenant_id = lclai.tenant_id
                                       and lcli.cust_limit_id = lclai.cust_limit_id
                                       and lcli.limit_object_id = lclai.cust_no
               where lcli.limit_template_id in ('HNNSDGKHEDTX')
                 and lcli.template_node_id in ('DGFSXED')) src
        on (
            tgt.tenant_id = src.tenant_id
                and tgt.cust_no = src.cust_no
            )
        when matched then
        update
            set tgt.no_crdt_lmt = src.no_crdt_lmt,
            tgt.update_time = sysdate
    </update>

    <!-- 7.更新担保额度 -->
    <update id="mergeGuaranteeLimit">
        merge into lc_corp_lmt_view tgt
        using (select lcli.tenant_id       as tenant_id,
                      lcli.limit_object_id as cust_no,
                      lcli.cust_limit_id,
                      lcli.template_node_id,
                      lclai.total_amount   as guar_lmt
               from lc_cust_limit_info lcli
                        inner join lc_cust_limit_amt_info lclai
                                   on lcli.tenant_id = lclai.tenant_id
                                       and lcli.cust_limit_id = lclai.cust_limit_id
                                       and lcli.limit_object_id = lclai.cust_no
               where lcli.limit_template_id in ('HNNSDGKHEDTX')
                 and lcli.template_node_id in ('DGPTDBED')) src
        on (
            tgt.tenant_id = src.tenant_id
                and tgt.cust_no = src.cust_no
            )
        when matched then
        update
            set tgt.guar_lmt = src.guar_lmt,
            tgt.update_time = sysdate
    </update>

    <!-- 8.更新合作方额度/合作方可用额度 -->
    <update id="mergeCoPartnerLimit">
        merge into lc_corp_lmt_view tgt
        using (select lcli.tenant_id           as tenant_id,
                      lcli.limit_object_id     as cust_no,
                      lcli.cust_limit_id,
                      lcli.template_node_id,
                      lclai.total_amount       as co_prtn_lmt,
                      lclai.total_amount - lclai.pre_occupy_amount -
                      lclai.real_occupy_amount as co_prtn_avl_lmt
               from lc_cust_limit_info lcli
                        inner join lc_cust_limit_amt_info lclai
                                   on lcli.tenant_id = lclai.tenant_id
                                       and lcli.cust_limit_id = lclai.cust_limit_id
                                       and lcli.limit_object_id = lclai.cust_no
               where lcli.limit_template_id in ('HNNSDGKHEDTX')
                 and lcli.template_node_id in ('DGHZFED')) src
        on (
            tgt.tenant_id = src.tenant_id
                and tgt.cust_no = src.cust_no
            )
        when matched then
        update
            set tgt.co_prtn_lmt = src.co_prtn_lmt,
            tgt.co_prtn_avl_lmt = src.co_prtn_avl_lmt,
            tgt.update_time = sysdate
    </update>

    <!-- 9.更新客户风险标签 -->
    <update id="mergeCustomerRiskTag">
        merge into lc_corp_lmt_view lclv
        using (select distinct lcli.tenant_id,
                               lcli.limit_object_id as cust_no,
                               case
                                   when max(case when instr(lcli.limit_status, '030') > 0 then 1 else 0 end) = 1
                                       then '01'
                                   else '02'
                                   end              as new_risk_tag
               FROM lc_cust_limit_info lcli
               WHERE lcli.limit_template_id = 'HNNSDGKHEDTX'
               GROUP BY lcli.tenant_id, lcli.limit_object_id) src
        on (lclv.tenant_id = src.tenant_id and lclv.cust_no = src.cust_no)
        when matched then
        update
            set lclv.cust_risk_tag = src.new_risk_tag,
            lclv.update_time = sysdate
    </update>

    <!-- 10.更新客户中心的相关字段 -->
    <update id="mergeCustomerCenterInfo">
        merge into lc_corp_lmt_view tgt
        using (select ltcci.tenant_id, ltcci.cust_no, ltcci.belg_inds_typ, ltcci.belg_grp_no, ltcci.belg_grp_nm
               from lb_t_corp_cust_info ltcci) src
        on (
            tgt.tenant_id = src.tenant_id
                and tgt.cust_no = src.cust_no
            )
        when matched then
        update
            set tgt.belg_inds_typ = src.belg_inds_typ,
            tgt.belg_grp_no = src.belg_grp_no,
            tgt.belg_grp_nm = src.belg_grp_nm,
            tgt.update_time = sysdate
    </update>

    <!-- 1.1.清空一般授信额度中间表 -->
    <update id="truncateCommonCreditLimit">
        TRUNCATE TABLE LB_T_RECLC_CORP_COM_CRDT
    </update>

    <!-- 1.2.插入一般授信额度客户编号和额度编号 -->
    <insert id="insertCommonCreditLimit">
        INSERT INTO LB_T_RECLC_CORP_COM_CRDT(CUST_NO, CUST_LIMIT_ID, TEMPLATE_NODE_ID, LIMIT_STATUS)
        SELECT DISTINCT LCLI.LIMIT_OBJECT_ID, LCLI.CUST_LIMIT_ID, lcli.TEMPLATE_NODE_ID, LCLI.LIMIT_STATUS
        FROM LC_CUST_LIMIT_INFO LCLI
        WHERE LCLI.LIMIT_TEMPLATE_ID IN ('HNNSDGKHEDTX')
          AND LCLI.TEMPLATE_NODE_ID IN ('DGYBSXED')
          AND LCLI.LIMIT_STATUS IN ('020')
    </insert>

    <!-- 1.3.更新一般授信额度中间表金额信息 -->
    <update id="mergeCommonCreditLimitAmount">
        MERGE INTO LB_T_RECLC_CORP_COM_CRDT TGT
        USING (SELECT LCLI.LIMIT_OBJECT_ID,
                      SUM(LCLAI.TOTAL_AMOUNT)             AS TOTAL_AMOUNT,
                      SUM(LCLAI.PRE_OCCUPY_AMOUNT)        AS PRE_OCCUPY_AMOUNT,
                      SUM(LCLAI.REAL_OCCUPY_AMOUNT)       AS REAL_OCCUPY_AMOUNT,
                      SUM(LCLAI.LOW_RISK_AMOUNT)          AS LOW_RISK_AMOUNT,
                      SUM(LCLAI.PRE_OCCUPY_LOW_RISK_AMT)  AS PRE_OCCUPY_LOW_RISK_AMT,
                      SUM(LCLAI.REAL_OCCUPY_LOW_RISK_AMT) AS REAL_OCCUPY_LOW_RISK_AMT
               FROM LC_CUST_LIMIT_INFO LCLI
                        INNER JOIN LC_CUST_LIMIT_AMT_INFO LCLAI
                                   ON LCLI.CUST_LIMIT_ID = LCLAI.CUST_LIMIT_ID AND
                                      LCLI.LIMIT_OBJECT_ID = LCLAI.CUST_NO
               WHERE LCLI.LIMIT_TEMPLATE_ID IN ('HNNSDGKHEDTX')
                 AND LCLI.TEMPLATE_NODE_ID IN ('DGZHSXDYCPED', 'DGDFXFXED')
                 AND LCLI.LIMIT_STATUS IN ('020')
                 AND LCLI.CUST_LIMIT_ID IN (SELECT CURRENT_NODE_LIMIT_ID
                                            FROM LC_CUST_LIMIT_RELATION LCLR
                                            WHERE LCLR.PARENT_NODE_LIMIT_ID IN (SELECT DISTINCT CUST_LIMIT_ID
                                                                                FROM LB_T_RECLC_CORP_COM_CRDT))
               GROUP BY LCLI.LIMIT_OBJECT_ID) SRC
        ON (TGT.CUST_NO = SRC.LIMIT_OBJECT_ID)
        WHEN MATCHED THEN
        UPDATE
            SET TGT.TOTAL_AMOUNT = SRC.TOTAL_AMOUNT,
            TGT.PRE_OCCUPY_AMOUNT = SRC.PRE_OCCUPY_AMOUNT,
            TGT.REAL_OCCUPY_AMOUNT = SRC.REAL_OCCUPY_AMOUNT,
            TGT.LOW_RISK_AMOUNT = SRC.LOW_RISK_AMOUNT,
            TGT.PRE_OCCUPY_LOW_RISK_AMT = SRC.PRE_OCCUPY_LOW_RISK_AMT,
            TGT.REAL_OCCUPY_LOW_RISK_AMT = SRC.REAL_OCCUPY_LOW_RISK_AMT
    </update>

    <!-- 1.4.更新额度实例金额信息 -->
    <update id="mergeCommonCreditLimitInstance">
        MERGE INTO LC_CUST_LIMIT_AMT_INFO TGT
        USING (SELECT CUST_NO,
                      CUST_LIMIT_ID,
                      LIMIT_STATUS,
                      TOTAL_AMOUNT,
                      PRE_OCCUPY_AMOUNT,
                      REAL_OCCUPY_AMOUNT,
                      LOW_RISK_AMOUNT,
                      PRE_OCCUPY_LOW_RISK_AMT,
                      REAL_OCCUPY_LOW_RISK_AMT
               FROM LB_T_RECLC_CORP_COM_CRDT) SRC
        ON (TGT.CUST_NO = SRC.CUST_NO
            AND TGT.CUST_LIMIT_ID = SRC.CUST_LIMIT_ID)
        WHEN MATCHED THEN
        UPDATE
            SET TGT.TOTAL_AMOUNT = SRC.TOTAL_AMOUNT,
            TGT.PRE_OCCUPY_AMOUNT = SRC.PRE_OCCUPY_AMOUNT,
            TGT.REAL_OCCUPY_AMOUNT = SRC.REAL_OCCUPY_AMOUNT,
            TGT.LOW_RISK_AMOUNT = SRC.LOW_RISK_AMOUNT,
            TGT.PRE_OCCUPY_LOW_RISK_AMT = SRC.PRE_OCCUPY_LOW_RISK_AMT,
            TGT.REAL_OCCUPY_LOW_RISK_AMT = SRC.REAL_OCCUPY_LOW_RISK_AMT,
            TGT.UPDATE_TIME = sysdate
    </update>

    <!-- 2.1.清空综合授信额度中间表 -->
    <update id="truncateComprehensiveCreditLimit">
        TRUNCATE TABLE LB_T_RECLC_CORP_CPRSV_CRDT
    </update>

    <!-- 2.2.插入综合授信额度客户编号和额度编号 -->
    <insert id="insertComprehensiveCreditLimit">
        INSERT INTO LB_T_RECLC_CORP_CPRSV_CRDT(CUST_NO, CUST_LIMIT_ID, TEMPLATE_NODE_ID, LIMIT_STATUS)
        SELECT DISTINCT LCLI.LIMIT_OBJECT_ID, LCLI.CUST_LIMIT_ID, lcli.TEMPLATE_NODE_ID, LCLI.LIMIT_STATUS
        FROM LC_CUST_LIMIT_INFO LCLI
        WHERE LCLI.LIMIT_TEMPLATE_ID IN ('HNNSDGKHEDTX')
          AND LCLI.TEMPLATE_NODE_ID IN ('DGZHSXED')
          AND LCLI.LIMIT_STATUS IN ('020')
    </insert>

    <!-- 2.3.更新综合授信额度中间表金额信息 -->
    <update id="mergeComprehensiveCreditLimitAmount">
        MERGE INTO LB_T_RECLC_CORP_CPRSV_CRDT TGT
        USING (SELECT LCLI.LIMIT_OBJECT_ID,
                      SUM(LCLAI.TOTAL_AMOUNT)             AS TOTAL_AMOUNT,
                      SUM(LCLAI.PRE_OCCUPY_AMOUNT)        AS PRE_OCCUPY_AMOUNT,
                      SUM(LCLAI.REAL_OCCUPY_AMOUNT)       AS REAL_OCCUPY_AMOUNT,
                      SUM(LCLAI.LOW_RISK_AMOUNT)          AS LOW_RISK_AMOUNT,
                      SUM(LCLAI.PRE_OCCUPY_LOW_RISK_AMT)  AS PRE_OCCUPY_LOW_RISK_AMT,
                      SUM(LCLAI.REAL_OCCUPY_LOW_RISK_AMT) AS REAL_OCCUPY_LOW_RISK_AMT
               FROM LC_CUST_LIMIT_INFO LCLI
                        INNER JOIN LC_CUST_LIMIT_AMT_INFO LCLAI
                                   ON LCLI.CUST_LIMIT_ID = LCLAI.CUST_LIMIT_ID AND
                                      LCLI.LIMIT_OBJECT_ID = LCLAI.CUST_NO
               WHERE LCLI.LIMIT_TEMPLATE_ID IN ('HNNSDGKHEDTX')
                 AND LCLI.TEMPLATE_NODE_ID IN ('DGZHSXDYCPED', 'DGDFXFXED')
                 AND LCLI.LIMIT_STATUS IN ('020')
                 AND LCLI.CUST_LIMIT_ID IN (SELECT CURRENT_NODE_LIMIT_ID
                                            FROM LC_CUST_LIMIT_RELATION LCLR
                                            WHERE LCLR.PARENT_NODE_LIMIT_ID IN (SELECT DISTINCT CUST_LIMIT_ID
                                                                                FROM LB_T_RECLC_CORP_CPRSV_CRDT))
               GROUP BY LCLI.LIMIT_OBJECT_ID) SRC
        ON (TGT.CUST_NO = SRC.LIMIT_OBJECT_ID)
        WHEN MATCHED THEN
        UPDATE
            SET TGT.TOTAL_AMOUNT = SRC.TOTAL_AMOUNT,
            TGT.PRE_OCCUPY_AMOUNT = SRC.PRE_OCCUPY_AMOUNT,
            TGT.REAL_OCCUPY_AMOUNT = SRC.REAL_OCCUPY_AMOUNT,
            TGT.LOW_RISK_AMOUNT = SRC.LOW_RISK_AMOUNT,
            TGT.PRE_OCCUPY_LOW_RISK_AMT = SRC.PRE_OCCUPY_LOW_RISK_AMT,
            TGT.REAL_OCCUPY_LOW_RISK_AMT = SRC.REAL_OCCUPY_LOW_RISK_AMT
    </update>

    <!-- 2.4.更新额度实例金额信息 -->
    <update id="mergeComprehensiveCreditLimitInstance">
        MERGE INTO LC_CUST_LIMIT_AMT_INFO TGT
        USING (SELECT CUST_NO,
                      CUST_LIMIT_ID,
                      LIMIT_STATUS,
                      TOTAL_AMOUNT,
                      PRE_OCCUPY_AMOUNT,
                      REAL_OCCUPY_AMOUNT,
                      LOW_RISK_AMOUNT,
                      PRE_OCCUPY_LOW_RISK_AMT,
                      REAL_OCCUPY_LOW_RISK_AMT
               FROM LB_T_RECLC_CORP_CPRSV_CRDT) SRC
        ON (TGT.CUST_NO = SRC.CUST_NO
            AND TGT.CUST_LIMIT_ID = SRC.CUST_LIMIT_ID)
        WHEN MATCHED THEN
        UPDATE
            SET TGT.TOTAL_AMOUNT = SRC.TOTAL_AMOUNT,
            TGT.PRE_OCCUPY_AMOUNT = SRC.PRE_OCCUPY_AMOUNT,
            TGT.REAL_OCCUPY_AMOUNT = SRC.REAL_OCCUPY_AMOUNT,
            TGT.LOW_RISK_AMOUNT = SRC.LOW_RISK_AMOUNT,
            TGT.PRE_OCCUPY_LOW_RISK_AMT = SRC.PRE_OCCUPY_LOW_RISK_AMT,
            TGT.REAL_OCCUPY_LOW_RISK_AMT = SRC.REAL_OCCUPY_LOW_RISK_AMT,
            TGT.UPDATE_TIME = sysdate
    </update>

    <!-- 3.1.清空单笔单批额度中间表 -->
    <update id="truncateSingleBatchLimit">
        TRUNCATE TABLE LB_T_RECLC_CORP_PER_REPLY
    </update>

    <!-- 3.2.插入单笔单批额度客户编号和额度编号 -->
    <insert id="insertSingleBatchLimit">
        INSERT INTO LB_T_RECLC_CORP_PER_REPLY(CUST_NO, CUST_LIMIT_ID, TEMPLATE_NODE_ID, LIMIT_STATUS)
        SELECT DISTINCT LCLI.LIMIT_OBJECT_ID, LCLI.CUST_LIMIT_ID, lcli.TEMPLATE_NODE_ID, LCLI.LIMIT_STATUS
        FROM LC_CUST_LIMIT_INFO LCLI
        WHERE LCLI.LIMIT_TEMPLATE_ID IN ('HNNSDGKHEDTX')
          AND LCLI.TEMPLATE_NODE_ID IN ('DGDBDBED')
          AND LCLI.LIMIT_STATUS IN ('020')
    </insert>

    <!-- 3.3.更新单笔单批额度中间表金额信息 -->
    <update id="mergeSingleBatchLimitAmount">
        MERGE INTO LB_T_RECLC_CORP_PER_REPLY TGT
        USING (SELECT LCLI.LIMIT_OBJECT_ID,
                      SUM(LCLAI.TOTAL_AMOUNT)             AS TOTAL_AMOUNT,
                      SUM(LCLAI.PRE_OCCUPY_AMOUNT)        AS PRE_OCCUPY_AMOUNT,
                      SUM(LCLAI.REAL_OCCUPY_AMOUNT)       AS REAL_OCCUPY_AMOUNT,
                      SUM(LCLAI.LOW_RISK_AMOUNT)          AS LOW_RISK_AMOUNT,
                      SUM(LCLAI.PRE_OCCUPY_LOW_RISK_AMT)  AS PRE_OCCUPY_LOW_RISK_AMT,
                      SUM(LCLAI.REAL_OCCUPY_LOW_RISK_AMT) AS REAL_OCCUPY_LOW_RISK_AMT
               FROM LC_CUST_LIMIT_INFO LCLI
                        INNER JOIN LC_CUST_LIMIT_AMT_INFO LCLAI
                                   ON LCLI.CUST_LIMIT_ID = LCLAI.CUST_LIMIT_ID AND
                                      LCLI.LIMIT_OBJECT_ID = LCLAI.CUST_NO
               WHERE LCLI.LIMIT_TEMPLATE_ID IN ('HNNSDGKHEDTX')
                 AND LCLI.TEMPLATE_NODE_ID IN ('DGZHSXDYCPED', 'DGDFXFXED')
                 AND LCLI.LIMIT_STATUS IN ('020')
                 AND LCLI.CUST_LIMIT_ID IN (SELECT CURRENT_NODE_LIMIT_ID
                                            FROM LC_CUST_LIMIT_RELATION LCLR
                                            WHERE LCLR.PARENT_NODE_LIMIT_ID IN (SELECT DISTINCT CUST_LIMIT_ID
                                                                                FROM LB_T_RECLC_CORP_PER_REPLY))
               GROUP BY LCLI.LIMIT_OBJECT_ID) SRC
        ON (TGT.CUST_NO = SRC.LIMIT_OBJECT_ID)
        WHEN MATCHED THEN
        UPDATE
            SET TGT.TOTAL_AMOUNT = SRC.TOTAL_AMOUNT,
            TGT.PRE_OCCUPY_AMOUNT = SRC.PRE_OCCUPY_AMOUNT,
            TGT.REAL_OCCUPY_AMOUNT = SRC.REAL_OCCUPY_AMOUNT,
            TGT.LOW_RISK_AMOUNT = SRC.LOW_RISK_AMOUNT,
            TGT.PRE_OCCUPY_LOW_RISK_AMT = SRC.PRE_OCCUPY_LOW_RISK_AMT,
            TGT.REAL_OCCUPY_LOW_RISK_AMT = SRC.REAL_OCCUPY_LOW_RISK_AMT
    </update>

    <!-- 3.4.更新额度实例金额信息 -->
    <update id="mergeSingleBatchLimitInstance">
        MERGE INTO LC_CUST_LIMIT_AMT_INFO TGT
        USING (SELECT CUST_NO,
                      CUST_LIMIT_ID,
                      LIMIT_STATUS,
                      TOTAL_AMOUNT,
                      PRE_OCCUPY_AMOUNT,
                      REAL_OCCUPY_AMOUNT,
                      LOW_RISK_AMOUNT,
                      PRE_OCCUPY_LOW_RISK_AMT,
                      REAL_OCCUPY_LOW_RISK_AMT
               FROM LB_T_RECLC_CORP_PER_REPLY) SRC
        ON (TGT.CUST_NO = SRC.CUST_NO
            AND TGT.CUST_LIMIT_ID = SRC.CUST_LIMIT_ID)
        WHEN MATCHED THEN
        UPDATE
            SET TGT.TOTAL_AMOUNT = SRC.TOTAL_AMOUNT,
            TGT.PRE_OCCUPY_AMOUNT = SRC.PRE_OCCUPY_AMOUNT,
            TGT.REAL_OCCUPY_AMOUNT = SRC.REAL_OCCUPY_AMOUNT,
            TGT.LOW_RISK_AMOUNT = SRC.LOW_RISK_AMOUNT,
            TGT.PRE_OCCUPY_LOW_RISK_AMT = SRC.PRE_OCCUPY_LOW_RISK_AMT,
            TGT.REAL_OCCUPY_LOW_RISK_AMT = SRC.REAL_OCCUPY_LOW_RISK_AMT,
            TGT.UPDATE_TIME = sysdate
    </update>

    <!-- 4.1.清空合作方层额度中间表 -->
    <update id="truncateCoPartnerLevelLimit">
        TRUNCATE TABLE LB_T_RECLC_CORP_CO_PRTN_LVL
    </update>

    <!-- 4.2.插入合作方层额度客户编号和额度编号 -->
    <insert id="insertCoPartnerLevelLimit">
        INSERT INTO LB_T_RECLC_CORP_CO_PRTN_LVL(CUST_NO, CUST_LIMIT_ID, TEMPLATE_NODE_ID, LIMIT_STATUS)
        SELECT DISTINCT LCLI.LIMIT_OBJECT_ID, LCLI.CUST_LIMIT_ID, lcli.TEMPLATE_NODE_ID, LCLI.LIMIT_STATUS
        FROM LC_CUST_LIMIT_INFO LCLI
        WHERE LCLI.LIMIT_TEMPLATE_ID IN ('HNNSDGKHEDTX')
          AND LCLI.TEMPLATE_NODE_ID IN ('DGHZFCED')
          AND LCLI.LIMIT_STATUS IN ('020')
    </insert>

    <!-- 4.3.更新合作方层额度中间表金额信息 -->
    <update id="mergeCoPartnerLevelLimitAmount">
        MERGE INTO LB_T_RECLC_CORP_CO_PRTN_LVL TGT
        USING (SELECT LCLI.LIMIT_OBJECT_ID,
                      SUM(LCLAI.TOTAL_AMOUNT)             AS TOTAL_AMOUNT,
                      SUM(LCLAI.PRE_OCCUPY_AMOUNT)        AS PRE_OCCUPY_AMOUNT,
                      SUM(LCLAI.REAL_OCCUPY_AMOUNT)       AS REAL_OCCUPY_AMOUNT,
                      SUM(LCLAI.LOW_RISK_AMOUNT)          AS LOW_RISK_AMOUNT,
                      SUM(LCLAI.PRE_OCCUPY_LOW_RISK_AMT)  AS PRE_OCCUPY_LOW_RISK_AMT,
                      SUM(LCLAI.REAL_OCCUPY_LOW_RISK_AMT) AS REAL_OCCUPY_LOW_RISK_AMT
               FROM LC_CUST_LIMIT_INFO LCLI
                        INNER JOIN LC_CUST_LIMIT_AMT_INFO LCLAI
                                   ON LCLI.CUST_LIMIT_ID = LCLAI.CUST_LIMIT_ID AND
                                      LCLI.LIMIT_OBJECT_ID = LCLAI.CUST_NO
               WHERE LCLI.LIMIT_TEMPLATE_ID IN ('HNNSDGKHEDTX')
                 AND LCLI.TEMPLATE_NODE_ID IN ('DGZHSXDYCPED', 'DGDFXFXED')
                 AND LCLI.LIMIT_STATUS IN ('020')
                 AND LCLI.CUST_LIMIT_ID IN (SELECT CURRENT_NODE_LIMIT_ID
                                            FROM LC_CUST_LIMIT_RELATION LCLR
                                            WHERE LCLR.PARENT_NODE_LIMIT_ID IN (SELECT DISTINCT CUST_LIMIT_ID
                                                                                FROM LB_T_RECLC_CORP_CO_PRTN_LVL))
               GROUP BY LCLI.LIMIT_OBJECT_ID) SRC
        ON (TGT.CUST_NO = SRC.LIMIT_OBJECT_ID)
        WHEN MATCHED THEN
        UPDATE
            SET TGT.TOTAL_AMOUNT = SRC.TOTAL_AMOUNT,
            TGT.PRE_OCCUPY_AMOUNT = SRC.PRE_OCCUPY_AMOUNT,
            TGT.REAL_OCCUPY_AMOUNT = SRC.REAL_OCCUPY_AMOUNT,
            TGT.LOW_RISK_AMOUNT = SRC.LOW_RISK_AMOUNT,
            TGT.PRE_OCCUPY_LOW_RISK_AMT = SRC.PRE_OCCUPY_LOW_RISK_AMT,
            TGT.REAL_OCCUPY_LOW_RISK_AMT = SRC.REAL_OCCUPY_LOW_RISK_AMT
    </update>

    <!-- 4.4.更新额度实例金额信息 -->
    <update id="mergeCoPartnerLevelLimitInstance">
        MERGE INTO LC_CUST_LIMIT_AMT_INFO TGT
        USING (SELECT CUST_NO,
                      CUST_LIMIT_ID,
                      LIMIT_STATUS,
                      TOTAL_AMOUNT,
                      PRE_OCCUPY_AMOUNT,
                      REAL_OCCUPY_AMOUNT,
                      LOW_RISK_AMOUNT,
                      PRE_OCCUPY_LOW_RISK_AMT,
                      REAL_OCCUPY_LOW_RISK_AMT
               FROM LB_T_RECLC_CORP_CO_PRTN_LVL) SRC
        ON (TGT.CUST_NO = SRC.CUST_NO
            AND TGT.CUST_LIMIT_ID = SRC.CUST_LIMIT_ID)
        WHEN MATCHED THEN
        UPDATE
            SET TGT.TOTAL_AMOUNT = SRC.TOTAL_AMOUNT,
            TGT.PRE_OCCUPY_AMOUNT = SRC.PRE_OCCUPY_AMOUNT,
            TGT.REAL_OCCUPY_AMOUNT = SRC.REAL_OCCUPY_AMOUNT,
            TGT.LOW_RISK_AMOUNT = SRC.LOW_RISK_AMOUNT,
            TGT.PRE_OCCUPY_LOW_RISK_AMT = SRC.PRE_OCCUPY_LOW_RISK_AMT,
            TGT.REAL_OCCUPY_LOW_RISK_AMT = SRC.REAL_OCCUPY_LOW_RISK_AMT,
            TGT.UPDATE_TIME = sysdate
    </update>

    <!-- 5.1.清空合作方额度中间表 -->
    <update id="truncateCoPartnerLimit">
        TRUNCATE TABLE LB_T_RECLC_CORP_CO_PRTN
    </update>

    <!-- 5.2.插入合作方额度客户编号和额度编号 -->
    <insert id="insertCoPartnerLimit">
        INSERT INTO LB_T_RECLC_CORP_CO_PRTN(CUST_NO, CUST_LIMIT_ID, TEMPLATE_NODE_ID, LIMIT_STATUS)
        SELECT DISTINCT LCLI.LIMIT_OBJECT_ID, LCLI.CUST_LIMIT_ID, lcli.TEMPLATE_NODE_ID, LCLI.LIMIT_STATUS
        FROM LC_CUST_LIMIT_INFO LCLI
        WHERE LCLI.LIMIT_TEMPLATE_ID IN ('HNNSDGKHEDTX')
          AND LCLI.TEMPLATE_NODE_ID IN ('DGHZFED')
          AND LCLI.LIMIT_STATUS IN ('020')
    </insert>

    <!-- 5.3.更新合作方额度中间表金额信息 -->
    <update id="mergeCoPartnerLimitAmount">
        MERGE INTO LB_T_RECLC_CORP_CO_PRTN TGT
        USING (SELECT LCLI.LIMIT_OBJECT_ID,
                      SUM(LCLAI.TOTAL_AMOUNT)             AS TOTAL_AMOUNT,
                      SUM(LCLAI.PRE_OCCUPY_AMOUNT)        AS PRE_OCCUPY_AMOUNT,
                      SUM(LCLAI.REAL_OCCUPY_AMOUNT)       AS REAL_OCCUPY_AMOUNT,
                      SUM(LCLAI.LOW_RISK_AMOUNT)          AS LOW_RISK_AMOUNT,
                      SUM(LCLAI.PRE_OCCUPY_LOW_RISK_AMT)  AS PRE_OCCUPY_LOW_RISK_AMT,
                      SUM(LCLAI.REAL_OCCUPY_LOW_RISK_AMT) AS REAL_OCCUPY_LOW_RISK_AMT
               FROM LC_CUST_LIMIT_INFO LCLI
                        INNER JOIN LC_CUST_LIMIT_AMT_INFO LCLAI
                                   ON LCLI.CUST_LIMIT_ID = LCLAI.CUST_LIMIT_ID AND
                                      LCLI.LIMIT_OBJECT_ID = LCLAI.CUST_NO
               WHERE LCLI.LIMIT_TEMPLATE_ID IN ('HNNSDGKHEDTX')
                 AND LCLI.TEMPLATE_NODE_ID IN ('DGZHSXDYCPED', 'DGDFXFXED')
                 AND LCLI.LIMIT_STATUS IN ('020')
                 AND LCLI.CUST_LIMIT_ID IN (SELECT CURRENT_NODE_LIMIT_ID
                                            FROM LC_CUST_LIMIT_RELATION LCLR
                                            WHERE LCLR.PARENT_NODE_LIMIT_ID IN (SELECT DISTINCT CUST_LIMIT_ID
                                                                                FROM LB_T_RECLC_CORP_CO_PRTN))
               GROUP BY LCLI.LIMIT_OBJECT_ID) SRC
        ON (TGT.CUST_NO = SRC.LIMIT_OBJECT_ID)
        WHEN MATCHED THEN
        UPDATE
            SET TGT.TOTAL_AMOUNT = SRC.TOTAL_AMOUNT,
            TGT.PRE_OCCUPY_AMOUNT = SRC.PRE_OCCUPY_AMOUNT,
            TGT.REAL_OCCUPY_AMOUNT = SRC.REAL_OCCUPY_AMOUNT,
            TGT.LOW_RISK_AMOUNT = SRC.LOW_RISK_AMOUNT,
            TGT.PRE_OCCUPY_LOW_RISK_AMT = SRC.PRE_OCCUPY_LOW_RISK_AMT,
            TGT.REAL_OCCUPY_LOW_RISK_AMT = SRC.REAL_OCCUPY_LOW_RISK_AMT
    </update>

    <!-- 5.4.更新额度实例金额信息 -->
    <update id="mergeCoPartnerLimitInstance">
        MERGE INTO LC_CUST_LIMIT_AMT_INFO TGT
        USING (SELECT CUST_NO,
                      CUST_LIMIT_ID,
                      LIMIT_STATUS,
                      TOTAL_AMOUNT,
                      PRE_OCCUPY_AMOUNT,
                      REAL_OCCUPY_AMOUNT,
                      LOW_RISK_AMOUNT,
                      PRE_OCCUPY_LOW_RISK_AMT,
                      REAL_OCCUPY_LOW_RISK_AMT
               FROM LB_T_RECLC_CORP_CO_PRTN) SRC
        ON (TGT.CUST_NO = SRC.CUST_NO
            AND TGT.CUST_LIMIT_ID = SRC.CUST_LIMIT_ID)
        WHEN MATCHED THEN
        UPDATE
            SET TGT.TOTAL_AMOUNT = SRC.TOTAL_AMOUNT,
            TGT.PRE_OCCUPY_AMOUNT = SRC.PRE_OCCUPY_AMOUNT,
            TGT.REAL_OCCUPY_AMOUNT = SRC.REAL_OCCUPY_AMOUNT,
            TGT.LOW_RISK_AMOUNT = SRC.LOW_RISK_AMOUNT,
            TGT.PRE_OCCUPY_LOW_RISK_AMT = SRC.PRE_OCCUPY_LOW_RISK_AMT,
            TGT.REAL_OCCUPY_LOW_RISK_AMT = SRC.REAL_OCCUPY_LOW_RISK_AMT,
            TGT.UPDATE_TIME = sysdate
    </update>

    <!-- 6.1.清空非授信额度中间表 -->
    <update id="truncateNoCreditLimit">
        TRUNCATE TABLE LB_T_RECLC_CORP_NO_CRDT
    </update>

    <!-- 6.2.插入非授信额度客户编号和额度编号 -->
    <insert id="insertNoCreditLimit">
        INSERT INTO LB_T_RECLC_CORP_NO_CRDT(CUST_NO, CUST_LIMIT_ID, TEMPLATE_NODE_ID, LIMIT_STATUS)
        SELECT DISTINCT LCLI.LIMIT_OBJECT_ID, LCLI.CUST_LIMIT_ID, lcli.TEMPLATE_NODE_ID, LCLI.LIMIT_STATUS
        FROM LC_CUST_LIMIT_INFO LCLI
        WHERE LCLI.LIMIT_TEMPLATE_ID IN ('HNNSDGKHEDTX')
          AND LCLI.TEMPLATE_NODE_ID IN ('DGFXED')
          AND LCLI.LIMIT_STATUS IN ('020')
    </insert>

    <!-- 6.3.更新非授信额度中间表金额信息 -->
    <update id="mergeNoCreditLimitAmount">
        MERGE INTO LB_T_RECLC_CORP_NO_CRDT TGT
        USING (SELECT LCLI.LIMIT_OBJECT_ID,
                      SUM(LCLAI.TOTAL_AMOUNT)             AS TOTAL_AMOUNT,
                      SUM(LCLAI.PRE_OCCUPY_AMOUNT)        AS PRE_OCCUPY_AMOUNT,
                      SUM(LCLAI.REAL_OCCUPY_AMOUNT)       AS REAL_OCCUPY_AMOUNT,
                      SUM(LCLAI.LOW_RISK_AMOUNT)          AS LOW_RISK_AMOUNT,
                      SUM(LCLAI.PRE_OCCUPY_LOW_RISK_AMT)  AS PRE_OCCUPY_LOW_RISK_AMT,
                      SUM(LCLAI.REAL_OCCUPY_LOW_RISK_AMT) AS REAL_OCCUPY_LOW_RISK_AMT
               FROM LC_CUST_LIMIT_INFO LCLI
                        INNER JOIN LC_CUST_LIMIT_AMT_INFO LCLAI
                                   ON LCLI.CUST_LIMIT_ID = LCLAI.CUST_LIMIT_ID AND
                                      LCLI.LIMIT_OBJECT_ID = LCLAI.CUST_NO
               WHERE LCLI.LIMIT_TEMPLATE_ID IN ('HNNSDGKHEDTX')
                 AND LCLI.TEMPLATE_NODE_ID IN ('DGZHSXDYCPED', 'DGDFXFXED')
                 AND LCLI.LIMIT_STATUS IN ('020')
                 AND LCLI.CUST_LIMIT_ID IN (SELECT CURRENT_NODE_LIMIT_ID
                                            FROM LC_CUST_LIMIT_RELATION LCLR
                                            WHERE LCLR.PARENT_NODE_LIMIT_ID IN (SELECT DISTINCT CUST_LIMIT_ID
                                                                                FROM LB_T_RECLC_CORP_NO_CRDT))
               GROUP BY LCLI.LIMIT_OBJECT_ID) SRC
        ON (TGT.CUST_NO = SRC.LIMIT_OBJECT_ID)
        WHEN MATCHED THEN
        UPDATE
            SET TGT.TOTAL_AMOUNT = SRC.TOTAL_AMOUNT,
            TGT.PRE_OCCUPY_AMOUNT = SRC.PRE_OCCUPY_AMOUNT,
            TGT.REAL_OCCUPY_AMOUNT = SRC.REAL_OCCUPY_AMOUNT,
            TGT.LOW_RISK_AMOUNT = SRC.LOW_RISK_AMOUNT,
            TGT.PRE_OCCUPY_LOW_RISK_AMT = SRC.PRE_OCCUPY_LOW_RISK_AMT,
            TGT.REAL_OCCUPY_LOW_RISK_AMT = SRC.REAL_OCCUPY_LOW_RISK_AMT
    </update>

    <!-- 6.4.更新额度实例金额信息 -->
    <update id="mergeNoCreditLimitInstance">
        MERGE INTO LC_CUST_LIMIT_AMT_INFO TGT
        USING (SELECT CUST_NO,
                      CUST_LIMIT_ID,
                      LIMIT_STATUS,
                      TOTAL_AMOUNT,
                      PRE_OCCUPY_AMOUNT,
                      REAL_OCCUPY_AMOUNT,
                      LOW_RISK_AMOUNT,
                      PRE_OCCUPY_LOW_RISK_AMT,
                      REAL_OCCUPY_LOW_RISK_AMT
               FROM LB_T_RECLC_CORP_NO_CRDT) SRC
        ON (TGT.CUST_NO = SRC.CUST_NO
            AND TGT.CUST_LIMIT_ID = SRC.CUST_LIMIT_ID)
        WHEN MATCHED THEN
        UPDATE
            SET TGT.TOTAL_AMOUNT = SRC.TOTAL_AMOUNT,
            TGT.PRE_OCCUPY_AMOUNT = SRC.PRE_OCCUPY_AMOUNT,
            TGT.REAL_OCCUPY_AMOUNT = SRC.REAL_OCCUPY_AMOUNT,
            TGT.LOW_RISK_AMOUNT = SRC.LOW_RISK_AMOUNT,
            TGT.PRE_OCCUPY_LOW_RISK_AMT = SRC.PRE_OCCUPY_LOW_RISK_AMT,
            TGT.REAL_OCCUPY_LOW_RISK_AMT = SRC.REAL_OCCUPY_LOW_RISK_AMT,
            TGT.UPDATE_TIME = sysdate
    </update>

    <!-- 7.1.清空客户总额度中间表 -->
    <update id="truncateTotalLimit">
        TRUNCATE TABLE LB_T_RECLC_CORP_TOTL
    </update>

    <!-- 7.2.插入客户总额度客户编号和额度编号 -->
    <insert id="insertTotalLimit">
        INSERT INTO LB_T_RECLC_CORP_TOTL(CUST_NO, CUST_LIMIT_ID, TEMPLATE_NODE_ID, LIMIT_STATUS)
        SELECT DISTINCT LCLI.LIMIT_OBJECT_ID, LCLI.CUST_LIMIT_ID, lcli.TEMPLATE_NODE_ID, LCLI.LIMIT_STATUS
        FROM LC_CUST_LIMIT_INFO LCLI
        WHERE LCLI.LIMIT_TEMPLATE_ID IN ('HNNSDGKHEDTX')
          AND LCLI.TEMPLATE_NODE_ID IN ('DGKHZED')
          AND LCLI.LIMIT_STATUS IN ('020')
    </insert>

    <!-- 7.3.更新客户总额度中间表金额信息 -->
    <update id="mergeTotalLimitAmount">
        MERGE INTO LB_T_RECLC_CORP_TOTL TGT
        USING (SELECT LCLI.LIMIT_OBJECT_ID,
                      SUM(LCLAI.TOTAL_AMOUNT)             AS TOTAL_AMOUNT,
                      SUM(LCLAI.PRE_OCCUPY_AMOUNT)        AS PRE_OCCUPY_AMOUNT,
                      SUM(LCLAI.REAL_OCCUPY_AMOUNT)       AS REAL_OCCUPY_AMOUNT,
                      SUM(LCLAI.LOW_RISK_AMOUNT)          AS LOW_RISK_AMOUNT,
                      SUM(LCLAI.PRE_OCCUPY_LOW_RISK_AMT)  AS PRE_OCCUPY_LOW_RISK_AMT,
                      SUM(LCLAI.REAL_OCCUPY_LOW_RISK_AMT) AS REAL_OCCUPY_LOW_RISK_AMT
               FROM LC_CUST_LIMIT_INFO LCLI
                        INNER JOIN LC_CUST_LIMIT_AMT_INFO LCLAI
                                   ON LCLI.CUST_LIMIT_ID = LCLAI.CUST_LIMIT_ID AND
                                      LCLI.LIMIT_OBJECT_ID = LCLAI.CUST_NO
               WHERE LCLI.LIMIT_TEMPLATE_ID IN ('HNNSDGKHEDTX')
                 AND LCLI.TEMPLATE_NODE_ID IN ('DGZHSXDYCPED', 'DGDFXFXED')
                 AND LCLI.LIMIT_STATUS IN ('020')
                 AND LCLI.CUST_LIMIT_ID IN (SELECT CURRENT_NODE_LIMIT_ID
                                            FROM LC_CUST_LIMIT_RELATION LCLR
                                            WHERE LCLR.PARENT_NODE_LIMIT_ID IN (SELECT DISTINCT CUST_LIMIT_ID
                                                                                FROM LB_T_RECLC_CORP_TOTL))
               GROUP BY LCLI.LIMIT_OBJECT_ID) SRC
        ON (TGT.CUST_NO = SRC.LIMIT_OBJECT_ID)
        WHEN MATCHED THEN
        UPDATE
            SET TGT.TOTAL_AMOUNT = SRC.TOTAL_AMOUNT,
            TGT.PRE_OCCUPY_AMOUNT = SRC.PRE_OCCUPY_AMOUNT,
            TGT.REAL_OCCUPY_AMOUNT = SRC.REAL_OCCUPY_AMOUNT,
            TGT.LOW_RISK_AMOUNT = SRC.LOW_RISK_AMOUNT,
            TGT.PRE_OCCUPY_LOW_RISK_AMT = SRC.PRE_OCCUPY_LOW_RISK_AMT,
            TGT.REAL_OCCUPY_LOW_RISK_AMT = SRC.REAL_OCCUPY_LOW_RISK_AMT
    </update>

    <!-- 7.4.更新额度实例金额信息 -->
    <update id="mergeTotalLimitInstance">
        MERGE INTO LC_CUST_LIMIT_AMT_INFO TGT
        USING (SELECT CUST_NO,
                      CUST_LIMIT_ID,
                      LIMIT_STATUS,
                      TOTAL_AMOUNT,
                      PRE_OCCUPY_AMOUNT,
                      REAL_OCCUPY_AMOUNT,
                      LOW_RISK_AMOUNT,
                      PRE_OCCUPY_LOW_RISK_AMT,
                      REAL_OCCUPY_LOW_RISK_AMT
               FROM LB_T_RECLC_CORP_TOTL) SRC
        ON (TGT.CUST_NO = SRC.CUST_NO
            AND TGT.CUST_LIMIT_ID = SRC.CUST_LIMIT_ID)
        WHEN MATCHED THEN
        UPDATE
            SET TGT.TOTAL_AMOUNT = SRC.TOTAL_AMOUNT,
            TGT.PRE_OCCUPY_AMOUNT = SRC.PRE_OCCUPY_AMOUNT,
            TGT.REAL_OCCUPY_AMOUNT = SRC.REAL_OCCUPY_AMOUNT,
            TGT.LOW_RISK_AMOUNT = SRC.LOW_RISK_AMOUNT,
            TGT.PRE_OCCUPY_LOW_RISK_AMT = SRC.PRE_OCCUPY_LOW_RISK_AMT,
            TGT.REAL_OCCUPY_LOW_RISK_AMT = SRC.REAL_OCCUPY_LOW_RISK_AMT,
            TGT.UPDATE_TIME = sysdate
    </update>
</mapper>