/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.common.enums;

import com.hsjry.base.common.job.dto.IEnumTrade;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * CODE小于32
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2023/3/6 14:57
 */
@Getter
@AllArgsConstructor
public enum EnumJobTrade implements IEnumTrade {

    /** 限额失效 */
    AMT_LIMIT_VALID("AMT_LIMIT_VALID", "限额失效", null),
    /** 限额计划调整 */
    AMT_LIMIT_PLAN_ADJUST("AMT_LIMIT_PLAN_ADJUST", "限额计划调整", null),
    /** 额度重算-余额占用重算准备 */
    BALANCE_LIMIT_OCCUPY_RETRY_CAL_PREPARE("BAL_LIMIT_RE_CAL_PREP", "额度重算-余额占用重算准备", null),
    /** 额度重算-余额占用重算 */
    BALANCE_LIMIT_OCCUPY_RETRY_CAL("BAL_LIMIT_RE_CAL", "额度重算-余额占用重算", null),
    /** 额度重算-合同占用重算准备 */
    CONTRACT_LIMIT_OCCUPY_RETRY_CAL_PREPARE("CON_LIMIT_RE_CAL_PREP", "额度重算-合同占用重算准备", null),
    /** 额度重算-合同占用重算 */
    CONTRACT_LIMIT_OCCUPY_RETRY_CAL("CON_LIMIT_RE_CAL", "额度重算-合同占用重算", null),
    /** 清理重算准备 */
    CLEAR_RE_CAL_PREPARE("CLEAR_RE_CAL_PREPARE", "清理重算准备", null),
    /** 额度到期 */
    CUST_LIMIT_EXPIRE("CUST_LIMIT_EXPIRE", "额度到期", null),
    /** 额度失效 */
    CUST_LIMIT_INVALID("CUST_LIMIT_INVALID", "额度失效", null),
    /** 限额占用重算 */
    AMT_LIMIT_RE_CAL("AMT_LIMIT_RE_CAL", "限额占用重算", null),
    /** 实体预发放失效 */
    ENTITY_PRE_GRANT_INVALID("ENTITY_PRE_GRANT_INVALID", "实体预发放失效", null),
    /** 实体汇率版本变更 */
    ENTITY_EXCHANGE_RATE_VERSION_NEXT("ENTITY_EX_RATE_VER_NEXT", "实体汇率版本变更", null),
    /** 限额日报生成 */
    AMT_LIMIT_DAILY_REPORT("AMT_LIMIT_DAILY_REPORT", "限额日报生成", null),
    /** 对账文件生成 */
    INBOUND_FILE("INBOUND_FILE", "对账文件生成", null),
    /** 本地文件合并 */
    MERGE_LOCAL_FILE("MERGE_LOCAL_FILE", "本地文件合并", null),
    MERGE_CREDIT_LOCAL_FILE("MERGE_CREDIT_LOCAL_FILE", "信贷下档本地文件合并", null),
    TX_CONTRACT_EXPIRE("TX_CONTRACT_EXPIRE", "同业额度到期", null),
    EXCHANGE("汇率文件同步", "汇率文件同步", null),
    /** 远程文件合并 */
    MERGE_REMOTE_FILE("MERGE_REMOTE_FILE", "远程文件合并", null),
    MERGE_CREDIT_REMOTE_FILE("MERGE_CREDIT_REMOTE_FILE", "信贷下档远程文件合并", null),

    /** 实体归档 */
    ENTITY_ARCHIVE("ENTITY_ARCHIVE", "实体归档", null),
    /** 限额停用前提醒 */
    AMT_LIMIT_DISABLE_REMIND("AMT_LIMIT_DISABLE_REMIND", "限额停用前提醒", null),
    /** 限额存量数据重算 */
    AMT_LIMIT_STOCK_CAL("AMT_LIMIT_STOCK_CAL", "限额存量数据重算", null),
    /** 单一限额停用 */
    DISABLE_SINGLE_LIMIT("DISABLE_SINGLE_LIMIT", "单一限额停用", null),
    /** 额度未使用失效 */
    CUST_LIMIT_NOT_USED_INVALID("CUST_LIMIT_NOT_USED_INVALID", "额度未使用失效", null),
    /** 额度重算-合同占用流水重算准备 */
    CON_LIMIT_SER_RE_CAL_PREP("CON_LIMIT_SER_RE_CAL_PREP", "额度重算-合同占用流水重算准备", null),
    /** 额度重算-合同占用流水重算 */
    CON_LIMIT_SER_RE_CAL("CON_LIMIT_SER_RE_CAL", "额度重算-合同占用流水重算", null),
    /** 统计机构额度准备 */
    STAT_ORGAN_LIMIT_PRE("STAT_ORGAN_LIMIT_PRE", "统计机构额度准备", null),
    /** 统计机构额度 */
    STATISTICS_ORGAN_LIMIT("STATISTICS_ORGAN_LIMIT", "统计机构额度", null),
    /** 核心借据文件同步 */
    CORE_INVOICE_FILE("CORE_INVOICE_FILE", "核心借据文件同步", null),
    /** 核心借据文件同步 */
    LS_CREDIT_INVOICE_FILE("LS_CREDIT_INVOICE_FILE", "零售信贷借据文件同步", null),
    /** 借据比对 */
    LOAN_INVOICE_COMPARE("LOAN_INVOICE_COMPARE", "借据比对", null),
    /** 借据还款 */
    LOAN_INVOICE_REPAY("LOAN_INVOICE_REPAY", "借据还款", null),
    /** 合同额度信息文件推送 */
    PUSH_CONTRACT_LIMIT("PUSH_CONTRACT_LIMIT", "合同额度信息文件推送", null),
    PUSH_CONTRACT_LIMIT_FILE("PUSH_CONTRACT_LIMIT_FILE", "合同额度信息文件推送(分片)", null),
    PUSH_CUSTOMER_LIMIT_FILE("PUSH_CUSTOMER_LIMIT_FILE", "客户额度信息文件推送(分片)", null),

    /** 客户额度信息文件推送 */
    PUSH_CUSTOMER_LIMIT("PUSH_CUSTOMER_LIMIT", "客户额度信息文件推送", null),
    /** 网贷借据文件同步-合作方额度场景 */
    PER_LOAN_INVOICE_FILE("PER_LOAN_INVOICE_FILE", "网贷借据文件同步-合作方额度场景", null),
    /** 网贷借据文件同步-联合贷场景 */
    JOINT_LOAN_INVOICE_FILE("JOINT_LOAN_INVOICE_FILE", "网贷借据文件同步-联合贷场景", null),
    /** 国结借据文件同步 */
    SETTLE_INVOICE_FILE("SETTLE_INVOICE_FILE", "国结借据文件同步", null),
    /** 国结垫款借据文件同步 */
    SETTLE_ADVANCED_INVOICE_FILE("SETTLE_ADVANCED_INVOICE_FILE", "国结垫款借据文件同步", null),
    BIG_DATA_SUPPLY("BIG_DATA_SUPPLY", "大数据下档", null),
    INVOICE_FILE_DOWNLOAD("INVOICE_FILE_DOWNLOAD", "借据文件下载", null),

    /** 汇总节点到期日重算 */
    COLLECT_LIMIT_TIME_RE_CALL("COLLECT_LIMIT_TIME_RE_CALL", "汇总节点到期日重算", null),
    // S_TABLE_DATA_CLEAR("S_TABLE_DATA_CLEAR", "落地表中数据清除", null),
    // C_TABLE_DATA_CLEAR("C_TABLE_DATA_CLEAR", "备份表中数据清除", null),
    // T_TABLE_DATA_CLEAR("T_TABLE_DATA_CLEAR", "临时表中数据清除", null),

    S_CORE_PPROD_FILE_SYNC("S_CORE_PPROD_FILE_SYNC", "核心系统-落地表-产品定义表文件同步", null),
    H_CORE_PPROD_FILE_SYNC("H_CORE_PPROD_FILE_SYNC", "核心系统-历史表-产品定义表文件同步", null),
    S_CORE_ADKZH_FILE_SYNC("S_CORE_ADKZH_FILE_SYNC", "核心系统-落地表-贷款账户主表文件同步", null),
    H_CORE_ADKZH_FILE_SYNC("H_CORE_ADKZH_FILE_SYNC", "核心系统-历史表-贷款账户主表文件同步", null),
    S_CORE_BCDHP_FILE_SYNC("S_CORE_BCDHP_FILE_SYNC", "核心系统-落地表-银承汇票登记簿文件同步", null),
    H_CORE_BCDHP_FILE_SYNC("H_CORE_BCDHP_FILE_SYNC", "核心系统-历史表-银承汇票登记簿文件同步", null),
    S_CORE_ATXZH_FILE_SYNC("S_CORE_ATXZH_FILE_SYNC", "核心系统-落地表-贴现账户主文件同步", null),
    H_CORE_ATXZH_FILE_SYNC("H_CORE_ATXZH_FILE_SYNC", "核心系统-历史表-贴现账户主文件同步", null),
    S_CORE_BTXPJ_FILE_SYNC("S_CORE_BTXPJ_FILE_SYNC", "核心系统-落地表-贴现票据信息文件同步", null),
    H_CORE_BTXPJ_FILE_SYNC("H_CORE_BTXPJ_FILE_SYNC", "核心系统-历史表-贴现票据信息文件同步", null),

    S_CPTL_BIZ_LMT_USE_STTN("S_CPTL_BIZ_LMT_USE_STTN", "资金系统-落地表-日终业务额度使用情况同步", null),
    H_CPTL_BIZ_LMT_USE_STTN("H_CPTL_BIZ_LMT_USE_STTN", "资金系统-历史表-日终业务额度使用情况同步", null),

    S_ITNST_LC_INFO_FILE_SYNC("S_ITNST_LC_INFO_FILE_SYNC", "国结系统-落地表-信用证信息文件同步", null),
    H_ITNST_LC_INFO_FILE_SYNC("H_ITNST_LC_INFO_FILE_SYNC", "国结系统-历史表-信用证信息文件同步", null),

    S_OL_PROD_LMT_INFO("S_OL_PROD_LMT_INFO", "网贷系统-落地表-产品额度信息", null),
    H_OL_PROD_LMT_INFO("H_OL_PROD_LMT_INFO", "网贷系统-历史表-产品额度信息", null),
    S_OL_CTR_INFO("S_OL_CTR_INFO", "网贷系统-落地表-合同信息", null),
    H_OL_CTR_INFO("H_OL_CTR_INFO", "网贷系统-历史表-合同信息", null),
    S_OL_LOAN_INFO("S_OL_LOAN_INFO", "网贷系统-落地表-借据信息", null),
    H_OL_LOAN_INFO("H_OL_LOAN_INFO", "网贷系统-历史表-借据信息", null),
    S_CCS_ACCT("S_CCS_ACCT", "信用卡-落地表-第一币种贷记帐户", null),
    H_CCS_ACCT("H_CCS_ACCT", "信用卡-历史表-第一币种贷记帐户", null),
    S_ELCBL_IBNK_LMT_SYNZ("S_ELCBL_IBNK_LMT_SYNZ", "电票系统-落地表-同业客户额度同步", null),
    H_ELCBL_IBNK_LMT_SYNZ("H_ELCBL_IBNK_LMT_SYNZ", "电票系统-历史表-同业客户额度同步", null),
    S_ELCBL_DSCT_BAL_INFO("S_ELCBL_DSCT_BAL_INFO", "电票系统-落地表-贴现余额信息", null),
    H_ELCBL_DSCT_BAL_INFO("H_ELCBL_DSCT_BAL_INFO", "电票系统-历史表-贴现余额信息", null),

    /** 额度实例信息文件同步 */
    C_LIMIT_INFO_BAK_SYNC("C_LIMIT_INFO_BAK_SYNC", "额度实例信息备份同步", null),

    /** 额度实例信息文件同步 */
    C_LIMIT_AMT_INFO_BAK_SYNC("C_LIMIT_AMT_INFO_BAK_SYNC", "额度实例金额信息数据备份同步", null),
    /** 额度实例所属对象信息文件同步 */
    C_LIMIT_OBJECT_INFO_BAK_SYNC("C_LIMIT_OBJECT_INFO_BAK_SYNC", "额度实例所属对象信息数据备份同步", null),
    /** 额度实例所属对象信息文件同步 */
    C_LIMIT_RELATION_BAK_SYNC("C_LIMIT_RELATION_BAK_SYNC", "额度实例关联信息数据备份同步", null),
    /** 额度实例所属对象信息文件同步 */
    C_LIMIT_OPERATE_SERIALNO_BAK_SYNC("C_LIMIT_OPERATE_SERIALNO_BAK_SYNC", "额度实例流水信息数据备份同步", null),
    C_ENTITY_INFO_BAK_SYNC("C_ENTITY_INFO_BAK_SYNC", "实体信息数据备份同步", null),
    C_ENTITY_OPERATE_SERIAL_BAK_SYNC("C_ENTITY_OPERATE_SERIAL_BAK_SYNC", "实体操作流水数据备份同步", null),

    LB_T_GRP_CUST_INFO_PROCESS("LB_T_GRP_CUST_INFO_PROCESS", "额度中心-中间表-集团客户信息处理", null),
    LB_T_CORP_CUST_INFO_PROCESS("LB_T_CORP_CUST_INFO_PROCESS", "额度中心-中间表-对公客户信息处理", null),
    LB_T_IBNK_CUST_INFO_PROCESS("LB_T_IBNK_CUST_INFO_PROCESS", "额度中心-中间表-同业客户信息处理", null),
    LB_T_INDV_CUST_INFO_PROCESS("LB_T_INDV_CUST_INFO_PROCESS", "额度中心-中间表-个人客户信息处理", null),
    LB_T_CCS_ACCT_LMT_PROCESS("LB_T_CCS_ACCT_LMT_PROCESS", "信用卡-中间表-信用卡额度信息处理", null),
    LB_T_ELCBL_IBNK_PROD_LMT_INFO_PROCESS("LB_T_ELCBL_IBNK_PROD_LMT_INFO_PROCESS",
        "电票系统-中间表-同业客户产品层额度信息处理", null),
    LB_T_ELCBL_CORP_LMT_INFO_PROCESS("LB_T_ELCBL_CORP_LMT_INFO_PROCESS", "电票系统-中间表-对公客户额度信息处理", null),
    LB_T_CPTL_PROD_LMT_INFO_PROCESS("LB_T_CPTL_PROD_LMT_INFO_PROCESS", "资金系统-中间表-产品层额度信息处理", null),

    LB_T_OL_CORP_LOAN_INFO_PROCESS("LB_T_OL_CORP_LOAN_INFO_PROCESS", "网贷系统-中间表-对公借据信息处理", null),
    LB_T_OL_CORP_CTR_INFO_PROCESS("LB_T_OL_CORP_CTR_INFO_PROCESS", "网贷系统-中间表-对公合同信息处理", null),
    LB_T_OL_CORP_PROD_LMT_INFO_PROCESS("LB_T_OL_CORP_PROD_LMT_INFO_PROCESS", "网贷系统-中间表-对公产品层额度信息处理",
        null),
    LB_T_OL_INDV_LOAN_INFO_PROCESS("LB_T_OL_INDV_LOAN_INFO_PROCESS", "网贷系统-中间表-个人借据信息处理", null),
    LB_T_OL_INDV_CTR_INFO_PROCESS("LB_T_OL_INDV_CTR_INFO_PROCESS", "网贷系统-中间表-个人合同信息处理", null),
    LB_T_OL_INDV_PROD_LMT_INFO_PROCESS("LB_T_OL_INDV_PROD_LMT_INFO_PROCESS", "网贷系统-中间表-个人产品层额度信息处理",
        null),

    LB_T_RECLC_CORP_PROCESS("LB_T_RECLC_CORP_PROCESS", "额度中心-对公客户额度重算", null),


    LC_CORP_LMT_VIEW_PROCESS("LC_CORP_LMT_VIEW_PROCESS", "额度中心-对公客户额度视图处理", null),
    LC_IBNK_LMT_VIEW_PROCESS("LC_IBNK_LMT_VIEW_PROCESS", "额度中心-同业客户额度视图处理", null),
    LC_GRP_LMT_VIEW_PROCESS("LC_GRP_LMT_VIEW_PROCESS", "额度中心-集团客户额度视图处理", null),
    LC_INDV_LMT_VIEW_PROCESS("LC_INDV_LMT_VIEW_PROCESS", "额度中心-个人客户额度视图处理", null),
    ;
    /**
     * 码
     **/
    private final String code;

    /**
     * 描述
     */
    private final String description;

    /**
     * 序号
     * 日终任务务必配置序号，以序号为执行顺序
     * 日间任务、定时任务 配置null
     */
    private final Integer order;

    public static EnumJobTrade find(String code) {
        for (EnumJobTrade tempEnum : EnumJobTrade.values()) {
            if (tempEnum.getCode().equals(code)) {
                return tempEnum;
            }
        }
        return null;
    }
}
