package com.hsjry.core.limit.batch.dal.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.hsjry.core.limit.batch.dal.dao.intf.LbTReclcCorpTotlDao;
import com.hsjry.core.limit.batch.dal.dao.mapper.LbTReclcCorpTotlMapper;
import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcCorpTotlDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcCorpTotlExample;
import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcCorpTotlKeyDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbTReclcCorpTotlQuery;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度中心-中间表-额度重算中对公客户中总额度数据库操作接口实现
 *
 * <AUTHOR>
 * @date 2025-08-21 11:31:28
 */
@Repository
public class LbTReclcCorpTotlDaoImpl extends AbstractBaseDaoImpl<LbTReclcCorpTotlDo, LbTReclcCorpTotlMapper>
    implements LbTReclcCorpTotlDao {
    /**
     * 分页查询
     *
     * @param lbTReclcCorpTotl 条件
     * @param pageParam 分页参数
     * @return
     */
    @Override
    public PageInfo<LbTReclcCorpTotlDo> selectPage(LbTReclcCorpTotlQuery lbTReclcCorpTotl, PageParam pageParam) {
        LbTReclcCorpTotlExample example = buildExample(lbTReclcCorpTotl);
        return PageHelper.<LbTReclcCorpTotlDo>startPage(pageParam.getPageNum(), pageParam.getPageSize())
            .doSelectPageInfo(() -> getMapper().selectByExample(example));
    }

    /**
     * 根据key查询额度中心-中间表-额度重算中对公客户中总额度
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    @Override
    public LbTReclcCorpTotlDo selectByKey(String custNo, String custLimitId) {
        LbTReclcCorpTotlKeyDo lbTReclcCorpTotlKeyDo = new LbTReclcCorpTotlKeyDo();
        lbTReclcCorpTotlKeyDo.setCustNo(custNo);
        lbTReclcCorpTotlKeyDo.setCustLimitId(custLimitId);
        return getMapper().selectByPrimaryKey(lbTReclcCorpTotlKeyDo);
    }

    /**
     * 根据key删除额度中心-中间表-额度重算中对公客户中总额度
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    @Override
    public int deleteByKey(String custNo, String custLimitId) {
        LbTReclcCorpTotlKeyDo lbTReclcCorpTotlKeyDo = new LbTReclcCorpTotlKeyDo();
        lbTReclcCorpTotlKeyDo.setCustNo(custNo);
        lbTReclcCorpTotlKeyDo.setCustLimitId(custLimitId);
        return getMapper().deleteByPrimaryKey(lbTReclcCorpTotlKeyDo);
    }

    /**
     * 查询额度中心-中间表-额度重算中对公客户中总额度信息
     *
     * @param lbTReclcCorpTotl 条件
     * @return List<LbTReclcCorpTotlDo>
     */
    @Override
    public List<LbTReclcCorpTotlDo> selectByExample(LbTReclcCorpTotlQuery lbTReclcCorpTotl) {
        return getMapper().selectByExample(buildExample(lbTReclcCorpTotl));
    }

    /**
     * 新增额度中心-中间表-额度重算中对公客户中总额度信息
     *
     * @param lbTReclcCorpTotl 条件
     * @return int>
     */
    @Override
    public int insertBySelective(LbTReclcCorpTotlDo lbTReclcCorpTotl) {
        if (lbTReclcCorpTotl == null) {
            return -1;
        }

        return getMapper().insertSelective(lbTReclcCorpTotl);
    }

    /**
     * 修改额度中心-中间表-额度重算中对公客户中总额度信息
     *
     * @param lbTReclcCorpTotl
     * @return
     */
    @Override
    public int updateBySelective(LbTReclcCorpTotlDo lbTReclcCorpTotl) {
        if (lbTReclcCorpTotl == null) {
            return -1;
        }

        return getMapper().updateByPrimaryKeySelective(lbTReclcCorpTotl);
    }

    @Override
    public int updateBySelectiveByExample(LbTReclcCorpTotlDo lbTReclcCorpTotl,
        LbTReclcCorpTotlQuery lbTReclcCorpTotlQuery) {
        return getMapper().updateByExampleSelective(lbTReclcCorpTotl, buildExample(lbTReclcCorpTotlQuery));
    }

    /**
     * 构建额度中心-中间表-额度重算中对公客户中总额度Example信息
     *
     * @param lbTReclcCorpTotl
     * @return
     */
    public LbTReclcCorpTotlExample buildExample(LbTReclcCorpTotlQuery lbTReclcCorpTotl) {
        LbTReclcCorpTotlExample example = new LbTReclcCorpTotlExample();
        LbTReclcCorpTotlExample.Criteria criteria = example.createCriteria();
        if (lbTReclcCorpTotl != null) {
            //添加查询条件
            if (StringUtil.isNotEmpty(lbTReclcCorpTotl.getCustNo())) {
                criteria.andCustNoEqualTo(lbTReclcCorpTotl.getCustNo());
            }
            if (StringUtil.isNotEmpty(lbTReclcCorpTotl.getCustLimitId())) {
                criteria.andCustLimitIdEqualTo(lbTReclcCorpTotl.getCustLimitId());
            }
            if (StringUtil.isNotEmpty(lbTReclcCorpTotl.getTemplateNodeId())) {
                criteria.andTemplateNodeIdEqualTo(lbTReclcCorpTotl.getTemplateNodeId());
            }
            if (StringUtil.isNotEmpty(lbTReclcCorpTotl.getLimitStatus())) {
                criteria.andLimitStatusEqualTo(lbTReclcCorpTotl.getLimitStatus());
            }
            if (null != lbTReclcCorpTotl.getTotalAmount()) {
                criteria.andTotalAmountEqualTo(lbTReclcCorpTotl.getTotalAmount());
            }
            if (null != lbTReclcCorpTotl.getPreOccupyAmount()) {
                criteria.andPreOccupyAmountEqualTo(lbTReclcCorpTotl.getPreOccupyAmount());
            }
            if (null != lbTReclcCorpTotl.getRealOccupyAmount()) {
                criteria.andRealOccupyAmountEqualTo(lbTReclcCorpTotl.getRealOccupyAmount());
            }
            if (null != lbTReclcCorpTotl.getLowRiskAmount()) {
                criteria.andLowRiskAmountEqualTo(lbTReclcCorpTotl.getLowRiskAmount());
            }
            if (null != lbTReclcCorpTotl.getPreOccupyLowRiskAmt()) {
                criteria.andPreOccupyLowRiskAmtEqualTo(lbTReclcCorpTotl.getPreOccupyLowRiskAmt());
            }
            if (null != lbTReclcCorpTotl.getRealOccupyLowRiskAmt()) {
                criteria.andRealOccupyLowRiskAmtEqualTo(lbTReclcCorpTotl.getRealOccupyLowRiskAmt());
            }
        }
        buildExampleExt(lbTReclcCorpTotl, criteria);
        return example;
    }

    /**
     * 构建额度中心-中间表-额度重算中对公客户中总额度ExampleExt方法
     *
     * @param lbTReclcCorpTotl
     * @return
     */
    public void buildExampleExt(LbTReclcCorpTotlQuery lbTReclcCorpTotl, LbTReclcCorpTotlExample.Criteria criteria) {

        //自定义实现
    }

}
