package com.hsjry.core.limit.batch.dal.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.hsjry.core.limit.batch.dal.dao.intf.LbTReclcCorpCoPrtnDao;
import com.hsjry.core.limit.batch.dal.dao.mapper.LbTReclcCorpCoPrtnMapper;
import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcCorpCoPrtnDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcCorpCoPrtnExample;
import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcCorpCoPrtnKeyDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbTReclcCorpCoPrtnQuery;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度中心-中间表-额度重算中对公客户中合作方额度数据库操作接口实现
 *
 * <AUTHOR>
 * @date 2025-08-21 11:31:28
 */
@Repository
public class LbTReclcCorpCoPrtnDaoImpl extends AbstractBaseDaoImpl<LbTReclcCorpCoPrtnDo, LbTReclcCorpCoPrtnMapper>
    implements LbTReclcCorpCoPrtnDao {
    /**
     * 分页查询
     *
     * @param lbTReclcCorpCoPrtn 条件
     * @param pageParam 分页参数
     * @return
     */
    @Override
    public PageInfo<LbTReclcCorpCoPrtnDo> selectPage(LbTReclcCorpCoPrtnQuery lbTReclcCorpCoPrtn, PageParam pageParam) {
        LbTReclcCorpCoPrtnExample example = buildExample(lbTReclcCorpCoPrtn);
        return PageHelper.<LbTReclcCorpCoPrtnDo>startPage(pageParam.getPageNum(), pageParam.getPageSize())
            .doSelectPageInfo(() -> getMapper().selectByExample(example));
    }

    /**
     * 根据key查询额度中心-中间表-额度重算中对公客户中合作方额度
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    @Override
    public LbTReclcCorpCoPrtnDo selectByKey(String custNo, String custLimitId) {
        LbTReclcCorpCoPrtnKeyDo lbTReclcCorpCoPrtnKeyDo = new LbTReclcCorpCoPrtnKeyDo();
        lbTReclcCorpCoPrtnKeyDo.setCustNo(custNo);
        lbTReclcCorpCoPrtnKeyDo.setCustLimitId(custLimitId);
        return getMapper().selectByPrimaryKey(lbTReclcCorpCoPrtnKeyDo);
    }

    /**
     * 根据key删除额度中心-中间表-额度重算中对公客户中合作方额度
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    @Override
    public int deleteByKey(String custNo, String custLimitId) {
        LbTReclcCorpCoPrtnKeyDo lbTReclcCorpCoPrtnKeyDo = new LbTReclcCorpCoPrtnKeyDo();
        lbTReclcCorpCoPrtnKeyDo.setCustNo(custNo);
        lbTReclcCorpCoPrtnKeyDo.setCustLimitId(custLimitId);
        return getMapper().deleteByPrimaryKey(lbTReclcCorpCoPrtnKeyDo);
    }

    /**
     * 查询额度中心-中间表-额度重算中对公客户中合作方额度信息
     *
     * @param lbTReclcCorpCoPrtn 条件
     * @return List<LbTReclcCorpCoPrtnDo>
     */
    @Override
    public List<LbTReclcCorpCoPrtnDo> selectByExample(LbTReclcCorpCoPrtnQuery lbTReclcCorpCoPrtn) {
        return getMapper().selectByExample(buildExample(lbTReclcCorpCoPrtn));
    }

    /**
     * 新增额度中心-中间表-额度重算中对公客户中合作方额度信息
     *
     * @param lbTReclcCorpCoPrtn 条件
     * @return int>
     */
    @Override
    public int insertBySelective(LbTReclcCorpCoPrtnDo lbTReclcCorpCoPrtn) {
        if (lbTReclcCorpCoPrtn == null) {
            return -1;
        }

        return getMapper().insertSelective(lbTReclcCorpCoPrtn);
    }

    /**
     * 修改额度中心-中间表-额度重算中对公客户中合作方额度信息
     *
     * @param lbTReclcCorpCoPrtn
     * @return
     */
    @Override
    public int updateBySelective(LbTReclcCorpCoPrtnDo lbTReclcCorpCoPrtn) {
        if (lbTReclcCorpCoPrtn == null) {
            return -1;
        }

        return getMapper().updateByPrimaryKeySelective(lbTReclcCorpCoPrtn);
    }

    @Override
    public int updateBySelectiveByExample(LbTReclcCorpCoPrtnDo lbTReclcCorpCoPrtn,
        LbTReclcCorpCoPrtnQuery lbTReclcCorpCoPrtnQuery) {
        return getMapper().updateByExampleSelective(lbTReclcCorpCoPrtn, buildExample(lbTReclcCorpCoPrtnQuery));
    }

    /**
     * 构建额度中心-中间表-额度重算中对公客户中合作方额度Example信息
     *
     * @param lbTReclcCorpCoPrtn
     * @return
     */
    public LbTReclcCorpCoPrtnExample buildExample(LbTReclcCorpCoPrtnQuery lbTReclcCorpCoPrtn) {
        LbTReclcCorpCoPrtnExample example = new LbTReclcCorpCoPrtnExample();
        LbTReclcCorpCoPrtnExample.Criteria criteria = example.createCriteria();
        if (lbTReclcCorpCoPrtn != null) {
            //添加查询条件
            if (StringUtil.isNotEmpty(lbTReclcCorpCoPrtn.getCustNo())) {
                criteria.andCustNoEqualTo(lbTReclcCorpCoPrtn.getCustNo());
            }
            if (StringUtil.isNotEmpty(lbTReclcCorpCoPrtn.getCustLimitId())) {
                criteria.andCustLimitIdEqualTo(lbTReclcCorpCoPrtn.getCustLimitId());
            }
            if (StringUtil.isNotEmpty(lbTReclcCorpCoPrtn.getTemplateNodeId())) {
                criteria.andTemplateNodeIdEqualTo(lbTReclcCorpCoPrtn.getTemplateNodeId());
            }
            if (StringUtil.isNotEmpty(lbTReclcCorpCoPrtn.getLimitStatus())) {
                criteria.andLimitStatusEqualTo(lbTReclcCorpCoPrtn.getLimitStatus());
            }
            if (null != lbTReclcCorpCoPrtn.getTotalAmount()) {
                criteria.andTotalAmountEqualTo(lbTReclcCorpCoPrtn.getTotalAmount());
            }
            if (null != lbTReclcCorpCoPrtn.getPreOccupyAmount()) {
                criteria.andPreOccupyAmountEqualTo(lbTReclcCorpCoPrtn.getPreOccupyAmount());
            }
            if (null != lbTReclcCorpCoPrtn.getRealOccupyAmount()) {
                criteria.andRealOccupyAmountEqualTo(lbTReclcCorpCoPrtn.getRealOccupyAmount());
            }
            if (null != lbTReclcCorpCoPrtn.getLowRiskAmount()) {
                criteria.andLowRiskAmountEqualTo(lbTReclcCorpCoPrtn.getLowRiskAmount());
            }
            if (null != lbTReclcCorpCoPrtn.getPreOccupyLowRiskAmt()) {
                criteria.andPreOccupyLowRiskAmtEqualTo(lbTReclcCorpCoPrtn.getPreOccupyLowRiskAmt());
            }
            if (null != lbTReclcCorpCoPrtn.getRealOccupyLowRiskAmt()) {
                criteria.andRealOccupyLowRiskAmtEqualTo(lbTReclcCorpCoPrtn.getRealOccupyLowRiskAmt());
            }
        }
        buildExampleExt(lbTReclcCorpCoPrtn, criteria);
        return example;
    }

    /**
     * 构建额度中心-中间表-额度重算中对公客户中合作方额度ExampleExt方法
     *
     * @param lbTReclcCorpCoPrtn
     * @return
     */
    public void buildExampleExt(LbTReclcCorpCoPrtnQuery lbTReclcCorpCoPrtn,
        LbTReclcCorpCoPrtnExample.Criteria criteria) {

        //自定义实现
    }

}
